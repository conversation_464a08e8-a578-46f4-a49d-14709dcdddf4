package com.example.fpt_app.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.repository.ExamRepository
import com.example.fpt_app.data.storage.RoomStorage
import com.example.fpt_app.ui.screen.BuildingTabsRoomSelectionScreen
import com.example.fpt_app.ui.screen.ExamDisplayScreen
import com.example.fpt_app.ui.screen.TVTestScreen

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    val context = LocalContext.current
    val roomStorage = RoomStorage(context)
    var selectedRepository: ExamRepository? = null
    var selectedRoom: Room? = null
    
    NavHost(
        navController = navController,
        startDestination = "room_selection"
    ) {
        composable("tv_test") {
            TVTestScreen(
                onBackClick = {
                    navController.navigate("room_selection")
                }
            )
        }

        composable("room_selection") {
            BuildingTabsRoomSelectionScreen(
                onRoomSelected = { room, repository ->
                    selectedRoom = room
                    selectedRepository = repository
                    navController.navigate("exam_display")
                }
            )
        }
        
        composable("exam_display") {
            selectedRoom?.let { room ->
                selectedRepository?.let { repository ->
                    ExamDisplayScreen(
                        room = room,
                        repository = repository,
                        onBackClick = {
                            navController.popBackStack()
                        },
                        onChangeRoom = {
                            android.util.Log.d("AppNavigation", "User wants to change room - clearing saved room")
                            // Clear saved room
                            roomStorage.clearSelectedRoom()
                            // Disconnect current WebSocket
                            repository.disconnect()
                            // Navigate back to room selection
                            navController.navigate("room_selection") {
                                popUpTo("room_selection") { inclusive = true }
                            }
                        }
                    )
                }
            }
        }
    }
}
