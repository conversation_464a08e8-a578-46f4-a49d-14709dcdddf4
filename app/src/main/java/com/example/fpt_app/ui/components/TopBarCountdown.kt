package com.example.fpt_app.ui.components

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccessTime
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun TopBarCountdown(
    examInfo: com.example.fpt_app.data.model.ExamCodePushDto.ExamInfo?,
    modifier: Modifier = Modifier
) {
    var timeRemaining by remember { mutableStateOf<Long?>(null) }
    var examStatus by remember { mutableStateOf(ExamStatus.NO_EXAM) }
    var isWarning by remember { mutableStateOf(false) }

    val warningScale by animateFloatAsState(
        targetValue = if (isWarning) 1.05f else 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(800),
            repeatMode = RepeatMode.Reverse
        ),
        label = "warning_scale"
    )

    LaunchedEffect(examInfo) {
        while (true) {
            // Use Vietnam timezone
            val vietnamZone = ZoneId.of("Asia/Ho_Chi_Minh")
            val now = LocalDateTime.now(vietnamZone)

            val vietnamTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            android.util.Log.d("VietnamTime", "Giờ hiện tại tại Việt Nam: ${now.format(vietnamTimeFormatter)}")

            examInfo?.let { info ->
                try {
                    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    val examEnd = LocalDateTime.parse(info.examEnd, formatter)

                    // Only calculate time remaining until exam end
                    val secondsRemaining = ChronoUnit.SECONDS.between(now, examEnd)

                    if (secondsRemaining > 0) {
                        examStatus = ExamStatus.ACTIVE
                        timeRemaining = secondsRemaining * 1000L
                        isWarning = secondsRemaining < 300 // Warning if less than 5 minutes
                        android.util.Log.d("TopBarCountdown", "Time remaining: $secondsRemaining seconds")
                    } else {
                        examStatus = ExamStatus.FINISHED
                        timeRemaining = 0L
                        isWarning = false
                        android.util.Log.d("TopBarCountdown", "Exam finished")
                    }
                } catch (e: Exception) {
                    android.util.Log.e("TopBarCountdown", "Error: ${e.message}")
                    examStatus = ExamStatus.ERROR
                    timeRemaining = null
                    isWarning = false
                }
            } ?: run {
                examStatus = ExamStatus.NO_EXAM
                timeRemaining = null
                isWarning = false
            }

            delay(1000)
        }
    }

    if (examInfo != null) {
        Row(
            modifier = modifier
                .scale(if (isWarning) warningScale else 1.0f)
                .background(
                    color = when (examStatus) {
                        ExamStatus.WAITING -> Color(0xFF6C7B7F)  // Professional gray-blue
                        ExamStatus.ACTIVE -> if (isWarning) Color(0xFFE53E3E) else Color(0xFF38A169)  // Red warning or green active
                        ExamStatus.FINISHED -> Color(0xFF718096)  // Neutral gray
                        ExamStatus.ERROR -> Color(0xFFE53E3E)  // Clean red
                        ExamStatus.NO_EXAM -> Color(0xFF718096)  // Neutral gray
                    },
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Clean icon
            Icon(
                imageVector = if (isWarning) Icons.Default.Warning else Icons.Default.AccessTime,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(18.dp)
            )

            // Status and countdown in single line
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = when (examStatus) {
                        ExamStatus.WAITING -> "BẮT ĐẦU SAU"
                        ExamStatus.ACTIVE -> if (isWarning) "CẢNH BÁO" else "CÒN LẠI"
                        ExamStatus.FINISHED -> "KẾT THÚC"
                        ExamStatus.ERROR -> "LỖI"
                        ExamStatus.NO_EXAM -> "CHƯA CÓ THI"
                    },
                    color = Color.White,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp
                )

                // Countdown display
                timeRemaining?.let { remaining ->
                    if (remaining > 0) {
                        val minutes = (remaining / 1000 / 60).toInt()
                        val seconds = ((remaining / 1000) % 60).toInt()

                        Text(
                            text = String.format("%02d:%02d", minutes, seconds),
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = if (isWarning) 16.sp else 15.sp,
                            letterSpacing = 1.sp
                        )
                    } else {
                        Text(
                            text = "00:00",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 15.sp
                        )
                    }
                } ?: run {
                    Text(
                        text = when (examStatus) {
                            ExamStatus.FINISHED -> "00:00"
                            ExamStatus.ERROR -> "--:--"
                            ExamStatus.NO_EXAM -> "--:--"
                            else -> "00:00"
                        },
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp
                    )
                }
            }
        }
    }
}

enum class ExamStatus {
    WAITING,
    ACTIVE,
    FINISHED,
    ERROR,
    NO_EXAM
}
