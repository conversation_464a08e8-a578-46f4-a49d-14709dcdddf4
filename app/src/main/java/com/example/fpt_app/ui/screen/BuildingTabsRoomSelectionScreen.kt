package com.example.fpt_app.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.ui.viewmodel.BuildingTabsViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BuildingTabsRoomSelectionScreen(
    onRoomSelected: (Room, com.example.fpt_app.data.repository.ExamRepository) -> Unit,
    viewModel: BuildingTabsViewModel = viewModel()
) {
    val buildings by viewModel.buildings.collectAsState()
    val selectedBuilding by viewModel.selectedBuilding.collectAsState()
    val rooms by viewModel.rooms.collectAsState()
    val selectedRoom by viewModel.selectedRoom.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val shouldShowRoomSelection by viewModel.shouldShowRoomSelection.collectAsState()

    var focusedRoomIndex by remember { mutableStateOf(0) }
    var focusedBuildingIndex by remember { mutableStateOf(0) }
    var isInBuildingMode by remember { mutableStateOf(false) }

    val gridState = rememberLazyGridState()

    LaunchedEffect(rooms) {
        if (rooms.isNotEmpty() && selectedRoom == null) {
            focusedRoomIndex = 0
            viewModel.selectRoom(rooms[0])
        }
    }

    LaunchedEffect(focusedRoomIndex, rooms.size) {
        if (rooms.isNotEmpty() && focusedRoomIndex < rooms.size) {
            gridState.animateScrollToItem(focusedRoomIndex)
        }
    }

    LaunchedEffect(selectedRoom, shouldShowRoomSelection) {
        if (!shouldShowRoomSelection && selectedRoom != null) {
            android.util.Log.d("BuildingTabsRoomSelection", "Auto-navigating to saved room: ${selectedRoom!!.name}")
            onRoomSelected(selectedRoom!!, viewModel.getRepository())
        }
    }
    
    if (!shouldShowRoomSelection) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7FA)),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2196F3),
                    strokeWidth = 3.dp,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Đang kết nối tới phòng đã lưu...",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFF666666)
                )
                selectedRoom?.let { room ->
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Phòng ${room.name}",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A1A1A)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = {
                            android.util.Log.d("BuildingTabsRoomSelection", "User requested to clear saved room")
                            viewModel.clearSavedRoom()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFF5722)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Chọn phòng khác",
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
        return
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F7FA))
            .focusable()
            .onKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.DirectionUp -> {
                            if (isInBuildingMode) {
                                // Already in building mode, can't go up further
                                android.util.Log.d("TVNavigation", "UP: Already in building mode")
                            } else {
                                // Try to move up one row first
                                if (focusedRoomIndex >= 5) {
                                    focusedRoomIndex -= 5
                                    android.util.Log.d("TVNavigation", "UP: Focus moved to room index $focusedRoomIndex")
                                } else {
                                    // If already in first row, switch to building mode
                                    isInBuildingMode = true
                                    focusedBuildingIndex = buildings.indexOfFirst { it.buildingName == selectedBuilding }.coerceAtLeast(0)
                                    android.util.Log.d("TVNavigation", "UP: Switched to building mode, focused building: $focusedBuildingIndex")
                                }
                            }
                            true
                        }
                        Key.DirectionDown -> {
                            if (isInBuildingMode) {
                                // Switch to room mode
                                isInBuildingMode = false
                                focusedRoomIndex = 0
                                android.util.Log.d("TVNavigation", "DOWN: Switched to room mode, focused room: $focusedRoomIndex")
                            } else {
                                // Navigate rooms down (move to next row)
                                val nextRowIndex = focusedRoomIndex + 5
                                if (nextRowIndex < rooms.size) {
                                    focusedRoomIndex = nextRowIndex
                                    android.util.Log.d("TVNavigation", "DOWN: Focus moved to room index $focusedRoomIndex")
                                } else {
                                    // If can't go down a full row, go to the last room
                                    val lastRowStart = (rooms.size - 1) / 5 * 5
                                    val currentColumn = focusedRoomIndex % 5
                                    val targetIndex = lastRowStart + currentColumn
                                    if (targetIndex < rooms.size && targetIndex != focusedRoomIndex) {
                                        focusedRoomIndex = targetIndex
                                        android.util.Log.d("TVNavigation", "DOWN: Focus moved to last row, room index $focusedRoomIndex")
                                    }
                                }
                            }
                            true
                        }
                        Key.DirectionLeft -> {
                            if (isInBuildingMode) {
                                // Navigate buildings left
                                if (focusedBuildingIndex > 0) {
                                    focusedBuildingIndex--
                                    android.util.Log.d("TVNavigation", "LEFT: Focus moved to building index $focusedBuildingIndex")
                                }
                            } else {
                                // Navigate rooms left
                                if (focusedRoomIndex > 0) {
                                    focusedRoomIndex--
                                    android.util.Log.d("TVNavigation", "LEFT: Focus moved to room index $focusedRoomIndex")
                                }
                            }
                            true
                        }
                        Key.DirectionRight -> {
                            if (isInBuildingMode) {
                                // Navigate buildings right
                                if (focusedBuildingIndex < buildings.size - 1) {
                                    focusedBuildingIndex++
                                    android.util.Log.d("TVNavigation", "RIGHT: Focus moved to building index $focusedBuildingIndex")
                                }
                            } else {
                                // Navigate rooms right
                                if (focusedRoomIndex < rooms.size - 1) {
                                    focusedRoomIndex++
                                    android.util.Log.d("TVNavigation", "RIGHT: Focus moved to room index $focusedRoomIndex")
                                }
                            }
                            true
                        }
                        Key.DirectionCenter, Key.Enter, Key.Spacebar -> {
                            if (isInBuildingMode) {
                                // Select building
                                if (buildings.isNotEmpty() && focusedBuildingIndex < buildings.size) {
                                    val building = buildings[focusedBuildingIndex]
                                    android.util.Log.d("TVNavigation", "SELECT: Building ${building.buildingName} selected")
                                    viewModel.selectBuilding(building.buildingName)
                                    isInBuildingMode = false
                                    focusedRoomIndex = 0
                                }
                            } else {
                                // Select room
                                if (rooms.isNotEmpty() && focusedRoomIndex < rooms.size) {
                                    val room = rooms[focusedRoomIndex]
                                    android.util.Log.d("TVNavigation", "SELECT: Room ${room.name} selected")
                                    viewModel.selectRoom(room)
                                    viewModel.getRepository().connectToRoom(room.id)
                                    onRoomSelected(room, viewModel.getRepository())
                                }
                            }
                            true
                        }
                        else -> false
                    }
                } else false
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp)
        ) {
            // Professional Header
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Compact FPT Logo
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                Color(0xFF1A1A1A),
                                RoundedCornerShape(8.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "FPT",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.ExtraBold,
                            color = Color.White,
                            letterSpacing = 1.sp
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "HỆ THỐNG HIỂN THỊ PHÒNG THI",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1A1A1A)
                        )
                        Text(
                            text = "TRƯỜNG ĐẠI HỌC FPT",
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF666666),
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Focus position indicator for TV navigation
                    if (rooms.isNotEmpty()) {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFF2196F3).copy(alpha = 0.1f)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "📍 ${focusedRoomIndex + 1}/${rooms.size}",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2196F3),
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))
                    }

                    // Debug button to clear saved room
                    IconButton(
                        onClick = {
                            android.util.Log.d("BuildingTabsRoomSelection", "Clearing saved room")
                            viewModel.clearSavedRoom()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Clear Saved Room",
                            tint = Color(0xFF999999),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }

            // Building Tabs Section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Business,
                    contentDescription = null,
                    tint = Color(0xFF1A1A1A),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "CHỌN TÒA NHÀ",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A1A1A)
                )
            }

            // Building Tabs
            if (buildings.isNotEmpty()) {
                ScrollableTabRow(
                    selectedTabIndex = buildings.indexOfFirst { it.buildingName == selectedBuilding },
                    modifier = Modifier.padding(bottom = 24.dp),
                    containerColor = Color.Transparent,
                    contentColor = Color(0xFF1A1A1A),
                    indicator = { },
                    divider = { }
                ) {
                    buildings.forEachIndexed { index, building ->
                        Tab(
                            selected = building.buildingName == selectedBuilding,
                            onClick = {
                                focusedBuildingIndex = index
                                viewModel.selectBuilding(building.buildingName)
                                isInBuildingMode = false
                                focusedRoomIndex = 0
                            },
                            modifier = Modifier
                                .padding(horizontal = 8.dp)
                                .border(
                                    width = when {
                                        isInBuildingMode && focusedBuildingIndex == index -> 3.dp
                                        building.buildingName == selectedBuilding -> 2.dp
                                        else -> 0.dp
                                    },
                                    color = when {
                                        isInBuildingMode && focusedBuildingIndex == index -> Color(0xFFFF9800) // Orange for TV focus
                                        building.buildingName == selectedBuilding -> Color(0xFF2196F3)
                                        else -> Color.Transparent
                                    },
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .background(
                                    color = when {
                                        isInBuildingMode && focusedBuildingIndex == index -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                        building.buildingName == selectedBuilding -> Color(0xFF2196F3).copy(alpha = 0.1f)
                                        else -> Color.Transparent
                                    },
                                    shape = RoundedCornerShape(8.dp)
                                )
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = building.buildingName,
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = when {
                                        isInBuildingMode && focusedBuildingIndex == index -> FontWeight.ExtraBold
                                        building.buildingName == selectedBuilding -> FontWeight.Bold
                                        else -> FontWeight.Medium
                                    },
                                    color = when {
                                        isInBuildingMode && focusedBuildingIndex == index -> Color(0xFFFF9800)
                                        building.buildingName == selectedBuilding -> Color(0xFF2196F3)
                                        else -> Color(0xFF666666)
                                    },
                                    modifier = Modifier.padding(vertical = 12.dp)
                                )

                                // Focus indicator for TV navigation
                                if (isInBuildingMode && focusedBuildingIndex == index) {
                                    Text(
                                        text = "NHẤN OK",
                                        style = MaterialTheme.typography.bodySmall,
                                        fontWeight = FontWeight.Bold,
                                        color = Color(0xFFFF9800),
                                        modifier = Modifier.padding(bottom = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // Room Selection Section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.MeetingRoom,
                    contentDescription = null,
                    tint = Color(0xFF1A1A1A),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "CHỌN PHÒNG THI",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A1A1A)
                )
                Spacer(modifier = Modifier.weight(1f))

                // Room count
                Text(
                    text = "${rooms.size} phòng",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFF666666),
                    fontWeight = FontWeight.Medium
                )
            }

            // Content area
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                when {
                    isLoading -> {
                        // Loading state
                        Column(
                            modifier = Modifier.fillMaxSize(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            CircularProgressIndicator(
                                color = Color(0xFF1A1A1A),
                                strokeWidth = 3.dp,
                                modifier = Modifier.size(48.dp)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Đang tải danh sách phòng...",
                                color = Color(0xFF666666),
                                style = MaterialTheme.typography.titleMedium
                            )
                        }
                    }

                    rooms.isEmpty() -> {
                        // Empty state
                        Column(
                            modifier = Modifier.fillMaxSize(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.ErrorOutline,
                                contentDescription = null,
                                tint = Color(0xFF999999),
                                modifier = Modifier.size(64.dp)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Không có phòng thi nào trong tòa ${selectedBuilding}",
                                color = Color(0xFF666666),
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Medium,
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    else -> {
                        // 5-column grid layout with proper scrolling
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(5),
                            state = gridState,
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(4.dp),
                            contentPadding = PaddingValues(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            itemsIndexed(rooms) { index, room ->
                                BuildingRoomCard(
                                    room = room,
                                    isSelected = selectedRoom == room,
                                    isFocused = focusedRoomIndex == index,
                                    onRoomClick = {
                                        focusedRoomIndex = index
                                        viewModel.selectRoom(room)
                                        viewModel.getRepository().connectToRoom(room.id)
                                        onRoomSelected(room, viewModel.getRepository())
                                    },
                                    onFocusChanged = { focused ->
                                        if (focused) focusedRoomIndex = index
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun BuildingRoomCard(
    room: Room,
    isSelected: Boolean,
    isFocused: Boolean,
    onRoomClick: () -> Unit,
    onFocusChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    var isCardFocused by remember { mutableStateOf(false) }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(0.8f)
            .focusable()
            .onFocusChanged { focusInfo ->
                isCardFocused = focusInfo.isFocused
                onFocusChanged(focusInfo.isFocused)
            }
            .onKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown) {
                    when (keyEvent.key) {
                        Key.DirectionCenter, Key.Enter, Key.Spacebar -> {
                            onRoomClick()
                            true
                        }
                        else -> false
                    }
                } else false
            }
            .clickable { onRoomClick() }
            .border(
                width = when {
                    isFocused || isCardFocused -> 4.dp  // Thicker border for TV
                    isSelected -> 3.dp
                    else -> 1.dp
                },
                color = when {
                    isFocused || isCardFocused -> Color(0xFF2196F3)
                    isSelected -> Color(0xFF4CAF50)
                    else -> Color(0xFFE0E0E0)
                },
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isSelected -> Color(0xFF4CAF50)
                isFocused || isCardFocused -> Color(0xFFE3F2FD)  // More visible focus color
                else -> Color.White
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = when {
                isFocused || isCardFocused -> 12.dp  // Higher elevation for TV focus
                isSelected -> 6.dp
                else -> 2.dp
            }
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Top section - Clean room display
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Room number in clean box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(45.dp)
                        .background(
                            color = when {
                                isSelected -> Color.White
                                else -> Color(0xFF1A1A1A)
                            },
                            shape = RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = room.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.ExtraBold,
                        color = when {
                            isSelected -> Color(0xFF4CAF50)
                            else -> Color.White
                        },
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Simple label
                Text(
                    text = "PHÒNG",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium,
                    color = when {
                        isSelected -> Color.White
                        else -> Color(0xFF666666)
                    },
                    textAlign = TextAlign.Center
                )
            }

            // Add some spacing
            Spacer(modifier = Modifier.height(8.dp))

            // Bottom section - Status & Indicator
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Status indicator
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(
                                Color(0xFF4CAF50),
                                CircleShape
                            )
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "ACTIVE",
                        style = MaterialTheme.typography.bodySmall,
                        color = when {
                            isSelected -> Color.White
                            else -> Color(0xFF4CAF50)
                        },
                        fontWeight = FontWeight.Medium
                    )
                }

                Spacer(modifier = Modifier.height(6.dp))

                // Enhanced Selection/Focus indicator
                when {
                    isSelected -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = "Selected",
                                tint = Color.White,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "ĐÃ CHỌN",
                                style = MaterialTheme.typography.bodySmall,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )
                        }
                    }
                    isFocused || isCardFocused -> {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Pulsing focus indicator
                            Box(
                                modifier = Modifier
                                    .size(8.dp)
                                    .background(
                                        Color(0xFF2196F3),
                                        CircleShape
                                    )
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "NHẤN OK",
                                style = MaterialTheme.typography.bodySmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2196F3),
                                modifier = Modifier
                                    .background(
                                        Color(0xFF2196F3).copy(alpha = 0.15f),
                                        RoundedCornerShape(6.dp)
                                    )
                                    .padding(horizontal = 8.dp, vertical = 3.dp)
                            )
                        }
                    }
                    else -> {
                        Spacer(modifier = Modifier.height(24.dp))
                    }
                }
            }
        }
    }
}
