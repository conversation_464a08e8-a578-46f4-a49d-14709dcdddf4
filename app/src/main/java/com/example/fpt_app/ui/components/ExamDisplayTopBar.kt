package com.example.fpt_app.ui.components

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.People
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.repository.ExamRepository
import com.example.fpt_app.data.websocket.WebSocketService

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExamDisplayTopBar(
    room: Room,
    repository: ExamRepository,
    connectionState: WebSocketService.ConnectionState,
    examCodes: List<com.example.fpt_app.data.model.ExamCodePushDto.ExamInfo>,
    onBackClick: () -> Unit,
    onChangeRoom: () -> Unit,
    onReconnect: () -> Unit
) {
    TopAppBar(
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Left side - Building and Room info
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    if (!room.building.isNullOrEmpty()) {
                        Text(
                            text = room.building,
                            color = Color(0xFF1976D2),
                            fontWeight = FontWeight.Bold,
                            style = MaterialTheme.typography.titleLarge
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "•",
                            color = Color(0xFF666666),
                            style = MaterialTheme.typography.titleLarge
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }

                    Text(
                        text = "PHÒNG ${room.name}",
                        color = Color(0xFF1A1A1A),
                        fontWeight = FontWeight.Medium,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                // Center - Countdown Timer
                TopBarCountdown(
                    examInfo = examCodes.firstOrNull(),
                    modifier = Modifier.wrapContentWidth()
                )

                // Right side - Empty space for balance
                Spacer(modifier = Modifier.weight(1f))
            }
        },
        navigationIcon = {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = Color(0xFF1A1A1A)
                )
            }
        },
        actions = {


            TextButton(
                onClick = onChangeRoom,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color(0xFFFF6B35)
                )
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = "Change Room",
                    tint = Color(0xFFFF6B35),
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "Đổi phòng",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }

            ConnectionStatusIndicator(connectionState, onReconnect)
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.White
        )
    )
}

@Composable
fun ConnectionStatusIndicator(
    connectionState: WebSocketService.ConnectionState,
    onReconnectClick: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(end = 8.dp)
    ) {
        when (connectionState) {
            WebSocketService.ConnectionState.CONNECTED -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Connected",
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(18.dp)
                )
            }
            WebSocketService.ConnectionState.CONNECTING -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(18.dp),
                    color = Color(0xFF2196F3),
                    strokeWidth = 2.dp
                )
            }
            WebSocketService.ConnectionState.DISCONNECTED,
            WebSocketService.ConnectionState.ERROR -> {
                IconButton(onClick = onReconnectClick) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Reconnect",
                        tint = Color(0xFFFF5722),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        }
    }
}
