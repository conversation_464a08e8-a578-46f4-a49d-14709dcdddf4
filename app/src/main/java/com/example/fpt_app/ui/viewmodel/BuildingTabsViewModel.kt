package com.example.fpt_app.ui.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.fpt_app.data.api.BuildingDetailResponse
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.repository.ExamRepository
import com.example.fpt_app.data.storage.RoomStorage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class BuildingTabsViewModel(application: Application) : AndroidViewModel(application) {
    private val repository = ExamRepository(application.applicationContext)
    private val roomStorage = RoomStorage(application.applicationContext)

    // Logging helpers
    private fun logInfo(message: String) {
        Log.i(TAG, "INFO: $message")
    }

    private fun logWarning(message: String) {
        Log.w(TAG, "WARNING: $message")
    }

    private fun logError(message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.e(TAG, "ERROR: $message", throwable)
        } else {
            Log.e(TAG, "ERROR: $message")
        }
    }

    private val _buildings = MutableStateFlow<List<BuildingDetailResponse>>(emptyList())
    val buildings: StateFlow<List<BuildingDetailResponse>> = _buildings.asStateFlow()

    private val _selectedBuilding = MutableStateFlow<String>("")
    val selectedBuilding: StateFlow<String> = _selectedBuilding.asStateFlow()

    private val _rooms = MutableStateFlow<List<Room>>(emptyList())
    val rooms: StateFlow<List<Room>> = _rooms.asStateFlow()

    private val _selectedRoom = MutableStateFlow<Room?>(null)
    val selectedRoom: StateFlow<Room?> = _selectedRoom.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _shouldShowRoomSelection = MutableStateFlow(true)
    val shouldShowRoomSelection: StateFlow<Boolean> = _shouldShowRoomSelection.asStateFlow()

    // Cache rooms by building to avoid repeated API calls
    private var roomsByBuilding: Map<String, List<Room>> = emptyMap()

    companion object {
        private const val TAG = "BuildingTabsViewModel"
        private const val DEFAULT_LOCATION = "FU-ĐN"
    }

    init {
        logInfo("Initializing BuildingTabsViewModel")
        checkSavedRoomAndLoad()
    }

    private fun checkSavedRoomAndLoad() {
        logInfo("=== CHECKING SAVED ROOM ===")

        if (roomStorage.hasSelectedRoom()) {
            val savedRoom = roomStorage.getSavedRoom()
            val savedBuilding = roomStorage.getSavedBuilding()

            if (savedRoom != null) {
                logInfo("Found saved room: ${savedRoom.name} (${savedRoom.id})")
                logInfo("Saved building: $savedBuilding")

                // Set saved room as selected
                _selectedRoom.value = savedRoom
                _selectedBuilding.value = savedBuilding ?: ""
                _shouldShowRoomSelection.value = false

                logInfo("Auto-connecting to saved room...")
                // Auto-connect to saved room
                repository.connectToRoom(savedRoom.id)

                return
            }
        }

        logInfo("No saved room found, showing room selection")
        _shouldShowRoomSelection.value = true
        loadBuildingsAndRooms()
    }

    private fun loadBuildingsAndRooms() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                logInfo("Loading campus data...")

                val campusData = repository.getCampusData()

                val buildings = campusData.buildings.map { building ->
                    BuildingDetailResponse(
                        buildingId = building.id,
                        locationName = campusData.campusName,
                        buildingName = building.buildingName
                    )
                }
                _buildings.value = buildings
                logInfo("Loaded ${buildings.size} buildings")

                // Extract rooms by building for quick access
                val roomsByBuilding = campusData.buildings.associate { building ->
                    building.buildingName to building.rooms
                }
                <EMAIL> = roomsByBuilding

                // Auto-select first building
                if (buildings.isNotEmpty()) {
                    val firstBuilding = buildings.first().buildingName
                    _selectedBuilding.value = firstBuilding
                    logInfo("Auto-selected first building: $firstBuilding")

                    // Load rooms for first building from cached data
                    val firstBuildingRooms = roomsByBuilding[firstBuilding] ?: emptyList()
                    _rooms.value = firstBuildingRooms
                    logInfo("Loaded ${firstBuildingRooms.size} rooms for building $firstBuilding")

                    // Auto-select first room
                    if (firstBuildingRooms.isNotEmpty()) {
                        _selectedRoom.value = firstBuildingRooms.first()
                        logInfo("Auto-selected first room: ${firstBuildingRooms.first().name}")
                    }
                } else {
                    logWarning("No buildings found from campus API")
                }

            } catch (e: Exception) {
                logError("Failed to load campus details", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun selectBuilding(buildingName: String) {
        logInfo("Building selected: $buildingName")
        _selectedBuilding.value = buildingName
        _selectedRoom.value = null // Clear room selection

        // Use cached rooms instead of API call
        val buildingRooms = roomsByBuilding[buildingName] ?: emptyList()
        _rooms.value = buildingRooms
        logInfo("Loaded ${buildingRooms.size} cached rooms for building $buildingName")

        // Auto-select first room
        if (buildingRooms.isNotEmpty()) {
            _selectedRoom.value = buildingRooms.first()
            logInfo("Auto-selected first room: ${buildingRooms.first().name}")
        }
    }
    
    fun selectRoom(room: Room) {
        logInfo("Room selected: ${room.name} in building ${_selectedBuilding.value}")
        _selectedRoom.value = room

        // Save selected room to storage
        roomStorage.saveSelectedRoom(room)
        logInfo("Room saved to storage for future auto-selection")
    }
    
    fun getRepository(): ExamRepository {
        return repository
    }
    
    fun reloadData() {
        logInfo("Manual reload triggered")
        loadBuildingsAndRooms()
    }

    // Get saved room for auto-navigation
    fun getSavedRoom(): Room? {
        return roomStorage.getSavedRoom()
    }

    // Clear saved room (for reset)
    fun clearSavedRoom() {
        logInfo("Clearing saved room")
        roomStorage.clearSelectedRoom()
        _shouldShowRoomSelection.value = true
        _selectedRoom.value = null
        _selectedBuilding.value = ""
    }

    // Force show room selection
    fun showRoomSelection() {
        logInfo("Forcing room selection display")
        _shouldShowRoomSelection.value = true
        loadBuildingsAndRooms()
    }

    // Get storage status for debugging
    fun getStorageStatus(): Map<String, String> {
        return roomStorage.getStorageStatus()
    }


}
