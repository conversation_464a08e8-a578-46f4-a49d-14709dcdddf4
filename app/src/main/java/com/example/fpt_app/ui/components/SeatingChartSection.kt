package com.example.fpt_app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.EventSeat
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.fpt_app.data.model.StudentSeatingPushDto

data class SeatInfo(
    val seatNumber: String,
    val studentCode: String? = null, // Last 3 digits of MSSV
    val studentName: String? = null,
    val isOccupied: Boolean = false
)

@Composable
fun SeatingChartSection(
    studentSeatings: List<StudentSeatingPushDto.StudentSeatingInfo>,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }

    // Generate seating data from real-time student data with safe handling
    val seatingData = remember(studentSeatings) {
        try {
            generateSeatingDataFromStudents(studentSeatings)
        } catch (e: Exception) {
            android.util.Log.e("SeatingChart", "Error in remember block", e)
            generateEmptySeatingData()
        }
    }

    Card(
        modifier = modifier
            .focusable()
            .onFocusChanged { focusInfo ->
                isFocused = focusInfo.isFocused
                android.util.Log.d("ExamDisplay", "Seating chart section focused: $isFocused")
            }
            .border(
                width = if (isFocused) 12.dp else 2.dp,
                color = if (isFocused) Color.Black else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(8.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isFocused) Color.Black.copy(alpha = 0.2f) else Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isFocused) 16.dp else 2.dp
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp)
        ) {
            // Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.EventSeat,
                    contentDescription = null,
                    tint = if (isFocused) Color.Black else Color(0xFF546E7A),
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = "SƠ ĐỒ CHỖ NGỒI",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (isFocused) Color.Black else Color(0xFF1A1A1A),
                    fontSize = if (isFocused) 20.sp else 16.sp
                )

                // Much more visible focus indicator
                if (isFocused) {
                    Spacer(modifier = Modifier.width(16.dp))
                    Text(
                        text = "🔸 ĐANG CHỌN 🔸",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.ExtraBold,
                        color = Color.White,
                        modifier = Modifier
                            .background(
                                Color.Black,
                                RoundedCornerShape(25.dp)
                            )
                            .padding(horizontal = 20.dp, vertical = 8.dp)
                    )
                }
            }

            // Seating Grid or Loading State
            if (studentSeatings.isNotEmpty()) {
                // Show seating grid when data is available
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp), // Reduced spacing to make it smaller
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    seatingData.forEach { rowSeats ->
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp), // Reduced spacing
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Seats in this row - smaller size
                            rowSeats.forEach { seat ->
                                SimpleSeatItem(seat = seat)
                            }
                        }
                    }
                }
            } else {
                // Show loading state when no seating data
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2196F3),
                            strokeWidth = 2.dp,
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "Đang tải sơ đồ chỗ ngồi",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF1A1A1A),
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Vui lòng đợi...",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF666666),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SimpleSeatItem(
    seat: SeatInfo,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(45.dp) // Smaller size to make room for larger exam codes
            .background(
                if (seat.isOccupied) Color(0xFFE3F2FD) else Color(0xFFF5F5F5), // Light blue if occupied, light gray if empty
                RoundedCornerShape(6.dp)
            )
            .border(
                width = if (seat.isOccupied) 2.dp else 1.dp,
                color = if (seat.isOccupied) Color(0xFF2196F3) else Color(0xFFBDBDBD), // Blue border for occupied, gray for empty
                shape = RoundedCornerShape(6.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Only show text when seat is occupied (has student from API)
            if (seat.isOccupied && seat.studentCode != null) {
                Text(
                    text = seat.studentCode,
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1976D2),
                    fontSize = 11.sp,
                    textAlign = TextAlign.Center
                )
            }
            // Empty seats show nothing (no text at all)
        }
    }
}

// Function to generate seating data from real student data
fun generateSeatingDataFromStudents(studentSeatings: List<StudentSeatingPushDto.StudentSeatingInfo>): List<List<SeatInfo>> {
    return try {
        val seatingData = mutableListOf<List<SeatInfo>>()

        // Remove duplicates and filter valid students
        val validStudents = studentSeatings
            .filter { student -> student.mssv.isNotBlank() }
            .distinctBy { it.mssv } // Remove duplicates by MSSV
            .take(24) // Limit to 24 seats (6 rows x 4 columns)

        android.util.Log.d("SeatingChart", "Processing ${validStudents.size} unique valid students (removed duplicates)")
        validStudents.forEachIndexed { index, student ->
            android.util.Log.d("SeatingChart", "Student $index: MSSV: ${student.mssv}")
        }

        // Create map: seat position -> student info
        val studentMap = mutableMapOf<String, StudentSeatingPushDto.StudentSeatingInfo>()
        validStudents.forEachIndexed { index, student ->
            val seatNumber = String.format(java.util.Locale.getDefault(), "%03d", index + 1)
            studentMap[seatNumber] = student
            android.util.Log.d("SeatingChart", "Assigned MSSV ${student.mssv} to seat $seatNumber")
        }

        // Generate 6 rows x 4 columns seating arrangement (24 seats total)
        for (row in 1..6) {
            val rowSeats = mutableListOf<SeatInfo>()
            for (col in 1..4) {
                val seatNumber = String.format(java.util.Locale.getDefault(), "%03d", (row - 1) * 4 + col)

                // Check if this seat has a student assigned
                val student = studentMap[seatNumber]

                val displayCode = if (student != null) {
                    try {
                        student.mssv.takeLast(3)
                    } catch (e: Exception) {
                        android.util.Log.e("SeatingChart", "Error getting MSSV", e)
                        "???"
                    }
                } else {
                    null // Empty seat will show nothing
                }

                rowSeats.add(
                    SeatInfo(
                        seatNumber = seatNumber,
                        studentCode = displayCode,
                        studentName = null, // No longer used
                        isOccupied = student != null
                    )
                )

                android.util.Log.d("SeatingChart", "Seat $seatNumber: ${if (student != null) "MSSV ${student.mssv} ($displayCode)" else "Empty"}")
                android.util.Log.d("SeatingChart", "  -> SeatInfo: seatNumber=$seatNumber, studentCode=$displayCode, isOccupied=${student != null}")
            }
            seatingData.add(rowSeats)
        }

        seatingData
    } catch (e: Exception) {
        android.util.Log.e("SeatingChart", "Error generating seating data", e)
        generateEmptySeatingData()
    }
}

private fun generateEmptySeatingData(): List<List<SeatInfo>> {
    val seatingData = mutableListOf<List<SeatInfo>>()
    for (row in 1..6) {
        val rowSeats = mutableListOf<SeatInfo>()
        for (col in 1..4) {
            val seatNumber = String.format(java.util.Locale.getDefault(), "%03d", (row - 1) * 4 + col)
            rowSeats.add(
                SeatInfo(
                    seatNumber = seatNumber,
                    studentCode = null,
                    studentName = null,
                    isOccupied = false
                )
            )
        }
        seatingData.add(rowSeats)
    }
    return seatingData
}
