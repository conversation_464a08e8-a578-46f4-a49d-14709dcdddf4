package com.example.fpt_app.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.repository.ExamRepository
import com.example.fpt_app.ui.components.ExamDisplayTopBar
import com.example.fpt_app.ui.components.ScrollableTVLayout
import com.example.fpt_app.ui.viewmodel.ExamDisplayViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExamDisplayScreen(
    room: Room,
    repository: ExamRepository,
    onBackClick: () -> Unit,
    onChangeRoom: () -> Unit = {}
) {
    val viewModel = remember { ExamDisplayViewModel(repository, room) }
    val connectionState by viewModel.connectionState.collectAsState()
    val examCodes by viewModel.examCodes.collectAsState()
    val notifications by viewModel.notifications.collectAsState()
    val studentSeatings by viewModel.studentSeatings.collectAsState()



    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFF5F5F5),
                        Color(0xFFE8E8E8)
                    )
                )
            )
    ) {
        // Top App Bar - Using component with countdown
        ExamDisplayTopBar(
            room = room,
            repository = repository,
            connectionState = connectionState,
            examCodes = examCodes,
            onBackClick = onBackClick,
            onChangeRoom = onChangeRoom,
            onReconnect = { viewModel.reconnect() }
        )

        // TV Layout - Using component
        ScrollableTVLayout(
            notifications = notifications,
            examCodes = examCodes,
            studentSeatings = studentSeatings,
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp)
        )
    }
}