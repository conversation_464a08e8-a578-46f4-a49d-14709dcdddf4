package com.example.fpt_app.ui.screen

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto
import com.example.fpt_app.ui.components.ScrollableTVLayout
import com.example.fpt_app.ui.components.TopBarCountdown
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TVTestScreen(
    onBackClick: () -> Unit
) {
    val testNotification = "🚨 THÔNG BÁO QUAN TRỌNG: Sinh viên vui lòng chuẩn bị đầy đủ giấy tờ tùy thân và thẻ sinh viên. Tuyệt đối không mang tài liệu và thiết bị điện tử vào phòng thi. Có mặt trước 15 phút!"


    fun getDefaultTestExamCodes(): List<ExamCodePushDto.ExamInfo> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val now = System.currentTimeMillis()

        return listOf(
            ExamCodePushDto.ExamInfo(
                examCode = "JAVA_SPRING_2024_001",
                roomName = "Phòng A101",
                subjectName = "Lập trình Java Spring Boot",
                examStart = dateFormat.format(Date(now + 60000L)), // 1 minute from now
                examEnd = dateFormat.format(Date(now + 3600000L)), // 1 hour from now
                openCode = "SPRING2024"
            ),
            ExamCodePushDto.ExamInfo(
                examCode = "DATABASE_MYSQL_2024_002",
                roomName = "Phòng A101",
                subjectName = "Cơ sở dữ liệu MySQL",
                examStart = dateFormat.format(Date(now + 7200000L)), // 2 hours from now
                examEnd = dateFormat.format(Date(now + 14400000L)), // 4 hours from now
                openCode = "MYSQL2024"
            ),
            ExamCodePushDto.ExamInfo(
                examCode = "ANDROID_DEV_2024_003",
                roomName = "Phòng A101",
                subjectName = "Phát triển ứng dụng Android",
                examStart = dateFormat.format(Date(now + 21600000L)), // 6 hours from now
                examEnd = dateFormat.format(Date(now + 28800000L)), // 8 hours from now
                openCode = null
            )
        )
    }

    var currentExamCodes by remember { mutableStateOf<List<ExamCodePushDto.ExamInfo>>(getDefaultTestExamCodes()) }

    fun generateCountdownTestExams(): List<ExamCodePushDto.ExamInfo> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        // Test exam 1: Starting in 2 minutes
        val exam1Start = Calendar.getInstance().apply { add(Calendar.MINUTE, 2) }
        val exam1End = Calendar.getInstance().apply { add(Calendar.HOUR, 1); add(Calendar.MINUTE, 2) }

        // Test exam 2: Active, 30 minutes remaining
        val exam2Start = Calendar.getInstance().apply { add(Calendar.MINUTE, -30) }
        val exam2End = Calendar.getInstance().apply { add(Calendar.MINUTE, 30) }

        // Test exam 3: Warning - 5 minutes remaining
        val exam3Start = Calendar.getInstance().apply { add(Calendar.MINUTE, -55) }
        val exam3End = Calendar.getInstance().apply { add(Calendar.MINUTE, 5) }

        return listOf(
            ExamCodePushDto.ExamInfo(
                examCode = "COUNTDOWN_TEST_001",
                roomName = "Phòng A101",
                subjectName = "Test - Bắt đầu sau 2 phút",
                examStart = dateFormat.format(exam1Start.time),
                examEnd = dateFormat.format(exam1End.time),
                openCode = "TEST001"
            ),
            ExamCodePushDto.ExamInfo(
                examCode = "COUNTDOWN_ACTIVE_002",
                roomName = "Phòng A102",
                subjectName = "Test - Đang thi, còn 30 phút",
                examStart = dateFormat.format(exam2Start.time),
                examEnd = dateFormat.format(exam2End.time),
                openCode = "ACTIVE002"
            ),
            ExamCodePushDto.ExamInfo(
                examCode = "COUNTDOWN_WARNING_003",
                roomName = "Phòng A103",
                subjectName = "Test - Cảnh báo 5 phút",
                examStart = dateFormat.format(exam3Start.time),
                examEnd = dateFormat.format(exam3End.time),
                openCode = "WARN003"
            )
        )
    }

    // Test student seating data
    val testStudentSeatings = listOf(
        StudentSeatingPushDto.StudentSeatingInfo(
            mssv = "SE161001"
        ),
        StudentSeatingPushDto.StudentSeatingInfo(
            mssv = "SE161002",
        ),
        StudentSeatingPushDto.StudentSeatingInfo(
            mssv = "SE161005",
        ),
        StudentSeatingPushDto.StudentSeatingInfo(
            mssv = "SE161010",
        )
    )

    var showNotification by remember { mutableStateOf<Boolean>(true) }
    var showStudentSeating by remember { mutableStateOf<Boolean>(true) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1976D2),
                        Color(0xFF42A5F5)
                    )
                )
            )
    ) {
        // Top App Bar - Enhanced with countdown timer
        TopAppBar(
            title = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Left side - Room info
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "GAMMA • PHÒNG A101",
                            color = Color.White,
                            fontWeight = FontWeight.ExtraBold,
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Text(
                            text = "Hệ thống hiển thị thi trực tuyến",
                            color = Color.White.copy(alpha = 0.9f),
                            fontWeight = FontWeight.Medium,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }

                    // Center - Countdown Timer
                    TopBarCountdown(
                        examInfo = currentExamCodes.firstOrNull(),
                        modifier = Modifier.wrapContentWidth()
                    )

                    // Right side - Empty space for balance
                    Spacer(modifier = Modifier.weight(1f))
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
            },
            actions = {
                // Toggle notification button
                TextButton(
                    onClick = { showNotification = !showNotification }
                ) {
                    Text(
                        text = if (showNotification) "Ẩn TB" else "Hiện TB",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }

                // Toggle student seating button
                TextButton(
                    onClick = { showStudentSeating = !showStudentSeating }
                ) {
                    Text(
                        text = if (showStudentSeating) "Ẩn SĐ" else "Hiện SĐ",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }

                // Test countdown timer button
                TextButton(
                    onClick = {
                        currentExamCodes = generateCountdownTestExams()
                        android.util.Log.d("TVTest", "Countdown test data generated: ${currentExamCodes.size} exam(s)")
                    }
                ) {
                    Text(
                        text = "Test ⏰",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }

                // Reset to default button
                TextButton(
                    onClick = {
                        currentExamCodes = getDefaultTestExamCodes()
                        android.util.Log.d("TVTest", "Reset to default exam codes")
                    }
                ) {
                    Text(
                        text = "Reset",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }

                TextButton(
                    onClick = {
                        val testBackendExamCodes = listOf(
                            ExamCodePushDto.ExamInfo(
                                examCode = "BE001",
                                openCode = "OPEN999",
                                roomName = "A101",
                                subjectName = "Backend Test Subject",
                                examStart = "2025-07-30 15:00:00",
                                examEnd = "2025-07-30 17:00:00",
                                rules = listOf(
                                    ExamCodePushDto.SubjectRuleResponse(
                                        subjectRuleId = "rule1",
                                        examType = "PRACTICAL",
                                        content = "Test rule from backend"
                                    )
                                )
                            )
                        )
                        currentExamCodes = testBackendExamCodes
                        android.util.Log.d("TVTest", "Backend test data created: ${testBackendExamCodes.size} exam(s)")
                    }
                ) {
                    Text(
                        text = "Test BE",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }

                TextButton(
                    onClick = {
                        android.util.Log.d("TVTest", "Clearing test notification")
                    }
                ) {
                    Text(
                        text = "Clear TB",
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        ScrollableTVLayout(
            notifications = if (showNotification) testNotification else "",
            examCodes = currentExamCodes,
            studentSeatings = if (showStudentSeating) testStudentSeatings else emptyList(),
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        )
    }

}
