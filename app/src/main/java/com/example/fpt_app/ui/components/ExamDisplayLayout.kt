package com.example.fpt_app.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto

@Composable
fun ScrollableTVLayout(
    notifications: String,
    examCodes: List<ExamCodePushDto.ExamInfo>,
    studentSeatings: List<StudentSeatingPushDto.StudentSeatingInfo>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Column(
            modifier = Modifier
                .weight(0.50f)
                .fillMaxHeight(),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            AlwaysExpandedNotificationSection(
                notifications = notifications,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.4f)
            )

            EnhancedExamCodesSection(
                examCodes = examCodes,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.6f)
            )
        }

        SeatingChartSection(
            studentSeatings = studentSeatings,
            modifier = Modifier
                .weight(0.25f)
                .fillMaxHeight()
        )

        SimplifiedRulesSection(
            modifier = Modifier
                .weight(0.25f)
                .fillMaxHeight()
        )
    }
}

