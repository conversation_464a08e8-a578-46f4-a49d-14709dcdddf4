package com.example.fpt_app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.repository.ExamRepository
import com.example.fpt_app.data.websocket.WebSocketService
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class ExamDisplayViewModel(
    private val repository: ExamRepository,
    private val room: Room
) : ViewModel() {
    
    val connectionState: StateFlow<WebSocketService.ConnectionState> = repository.connectionState
    val examCodes: StateFlow<List<ExamCodePushDto.ExamInfo>> = repository.examCodes
    val notifications: StateFlow<String> = repository.notifications
    val studentSeatings: StateFlow<List<StudentSeatingPushDto.StudentSeatingInfo>> = repository.studentSeatings
    
    init {
        connectToRoom()
    }
    
    private fun connectToRoom() {
        viewModelScope.launch {
            repository.connectToRoom(room.id)
        }
    }
    
    fun reconnect() {
        viewModelScope.launch {
            repository.disconnect()
            repository.connectToRoom(room.id)
        }
    }
    
    fun getRoomInfo(): Room {
        return room
    }
    
    override fun onCleared() {
        super.onCleared()
        repository.disconnect()
    }
}
