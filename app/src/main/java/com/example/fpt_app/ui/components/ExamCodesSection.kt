package com.example.fpt_app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Assignment
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.fpt_app.data.model.ExamCodePushDto

@Composable
fun EnhancedExamCodesSection(
    examCodes: List<ExamCodePushDto.ExamInfo>,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }

    Card(
        modifier = modifier
            .focusable()
            .onFocusChanged { focusInfo ->
                isFocused = focusInfo.isFocused
                android.util.Log.d("ExamDisplay", "Enhanced exam codes section focused: $isFocused")
            }
            .border(
                width = if (isFocused) 12.dp else 2.dp,
                color = if (isFocused) Color.Black else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(8.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isFocused) Color.Black.copy(alpha = 0.2f) else Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isFocused) 16.dp else 2.dp
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.Assignment,
                    contentDescription = null,
                    tint = if (isFocused) Color.Black else Color(0xFF4CAF50),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "MÃ THI",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = if (isFocused) Color.Black else Color(0xFF1A1A1A),
                    fontSize = if (isFocused) 22.sp else 18.sp
                )

                // Much more visible focus indicator
                if (isFocused) {
                    Spacer(modifier = Modifier.width(16.dp))
                    Text(
                        text = "🔸 ĐANG CHỌN 🔸",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.ExtraBold,
                        color = Color.White,
                        modifier = Modifier
                            .background(
                                Color.Black,
                                RoundedCornerShape(25.dp)
                            )
                            .padding(horizontal = 20.dp, vertical = 8.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))
            if (examCodes.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(examCodes) { examInfo ->
                        ExamCodeLineItem(
                            examCode = examInfo.examCode,
                            openCode = examInfo.openCode,
                            startTime = examInfo.examStart,
                            endTime = examInfo.examEnd,
                            rules = examInfo.rules
                        )
                    }
                }
            } else {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2196F3),
                            strokeWidth = 3.dp,
                            modifier = Modifier.size(40.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Vui lòng đợi mã môn thi",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF1A1A1A)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Admin đang chuẩn bị...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFF666666)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ExamCodeLineItem(
    examCode: String,
    openCode: String?,
    startTime: String,
    endTime: String,
    rules: List<ExamCodePushDto.SubjectRuleResponse>? = null
) {
    fun formatTime(timeString: String): String {
        return try {
            if (timeString.contains(" ")) {
                timeString.split(" ")[1].substring(0, 5)
            } else {
                timeString
            }
        } catch (_: Exception) {
            timeString
        }
    }

    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFBFBFB)
        ),
        shape = RoundedCornerShape(1.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = examCode,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1A1A1A),
                    fontSize = 16.sp,
                    maxLines = 2,
                    lineHeight = 18.sp
                )

                if (!openCode.isNullOrBlank()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "Code: $openCode",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                        fontSize = 12.sp
                    )
                }
            }

            Text(
                text = "${formatTime(startTime)}-${formatTime(endTime)}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF424242),
                fontSize = 13.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(start = 12.dp)
            )
        }

        if (!rules.isNullOrEmpty()) {
            val matchingRules = rules.filter { rule ->
                examCode.contains(rule.examType, ignoreCase = true)
            }

            if (matchingRules.isNotEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 0.dp)
                        .padding(bottom = 8.dp)
                ) {
                    matchingRules.take(1).forEach { rule ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    Color(0xFFFFF3CD),
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 8.dp, vertical = 6.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "⚠️",
                                fontSize = 12.sp,
                                modifier = Modifier.padding(end = 6.dp)
                            )

                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "QUY ĐỊNH ${rule.examType}:",
                                    style = MaterialTheme.typography.bodySmall,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFF856404),
                                    fontSize = 10.sp
                                )
                                Text(
                                    text = rule.content,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF664D03),
                                    fontSize = 11.sp,
                                    fontWeight = FontWeight.Medium,
                                    maxLines = 2
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
