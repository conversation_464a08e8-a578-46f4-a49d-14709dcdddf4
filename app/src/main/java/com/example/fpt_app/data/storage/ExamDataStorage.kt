package com.example.fpt_app.data.storage

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import androidx.core.content.edit

class  ExamDataStorage(context: Context) {
    companion object {
        private const val TAG = "ExamDataStorage"
        private const val PREFS_NAME = "exam_data_prefs"
        private const val KEY_EXAM_CODES = "exam_codes"
        private const val KEY_NOTIFICATIONS = "notifications"
        private const val KEY_STUDENT_SEATINGS = "student_seatings"
        private const val KEY_LAST_UPDATE = "last_update"
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    private fun logInfo(message: String) {
        Log.i(TAG, "INFO: $message")
    }

    private fun logError(message: String, throwable: Throwable? = null) {
        Log.e(TAG, "ERROR: $message", throwable)
    }

    fun saveExamCodes(roomId: String, examCodes: List<ExamCodePushDto.ExamInfo>) {
        try {
            val json = gson.toJson(examCodes)
            prefs.edit {
                putString("${KEY_EXAM_CODES}_$roomId", json)
                    .putLong("${KEY_LAST_UPDATE}_$roomId", System.currentTimeMillis())
            }
            logInfo("Saved ${examCodes.size} exam codes for room $roomId")
        } catch (e: Exception) {
            logError("Failed to save exam codes", e)
        }
    }

    fun getExamCodes(roomId: String): List<ExamCodePushDto.ExamInfo> {
        return try {
            val json = prefs.getString("${KEY_EXAM_CODES}_$roomId", null)
            if (json != null) {
                val type = object : TypeToken<List<ExamCodePushDto.ExamInfo>>() {}.type
                val examCodes = gson.fromJson<List<ExamCodePushDto.ExamInfo>>(json, type)
                logInfo("Retrieved ${examCodes.size} exam codes for room $roomId")
                examCodes
            } else {
                logInfo("No saved exam codes found for room $roomId")
                emptyList()
            }
        } catch (e: Exception) {
            logError("Failed to retrieve exam codes", e)
            emptyList()
        }
    }

    fun saveNotification(roomId: String, notification: String) {
        try {
            prefs.edit {
                putString("${KEY_NOTIFICATIONS}_$roomId", notification)
                    .putLong("${KEY_LAST_UPDATE}_$roomId", System.currentTimeMillis())
            }
            logInfo("Saved notification for room $roomId: ${notification.take(50)}...")
        } catch (e: Exception) {
            logError("Failed to save notification", e)
        }
    }

    fun getNotification(roomId: String): String {
        return try {
            val notification = prefs.getString("${KEY_NOTIFICATIONS}_$roomId", "")
            logInfo("Retrieved notification for room $roomId: ${notification?.take(50)}...")
            notification ?: ""
        } catch (e: Exception) {
            logError("Failed to retrieve notification", e)
            ""
        }
    }

    fun saveStudentSeatings(roomId: String, seatings: List<StudentSeatingPushDto.StudentSeatingInfo>) {
        try {
            val json = gson.toJson(seatings)
            prefs.edit {
                putString("${KEY_STUDENT_SEATINGS}_$roomId", json)
                    .putLong("${KEY_LAST_UPDATE}_$roomId", System.currentTimeMillis())
            }
            logInfo("Saved ${seatings.size} student seatings for room $roomId")
        } catch (e: Exception) {
            logError("Failed to save student seatings", e)
        }
    }

    fun getStudentSeatings(roomId: String): List<StudentSeatingPushDto.StudentSeatingInfo> {
        return try {
            val json = prefs.getString("${KEY_STUDENT_SEATINGS}_$roomId", null)
            if (json != null) {
                val type = object : TypeToken<List<StudentSeatingPushDto.StudentSeatingInfo>>() {}.type
                val seatings = gson.fromJson<List<StudentSeatingPushDto.StudentSeatingInfo>>(json, type)
                logInfo("Retrieved ${seatings.size} student seatings for room $roomId")
                seatings
            } else {
                logInfo("No saved student seatings found for room $roomId")
                emptyList()
            }
        } catch (e: Exception) {
            logError("Failed to retrieve student seatings", e)
            emptyList()
        }
    }

    fun getLastUpdateTime(roomId: String): Long {
        return prefs.getLong("${KEY_LAST_UPDATE}_$roomId", 0L)
    }

    fun clearRoomData(roomId: String) {
        try {
            prefs.edit {
                remove("${KEY_EXAM_CODES}_$roomId")
                    .remove("${KEY_NOTIFICATIONS}_$roomId")
                    .remove("${KEY_STUDENT_SEATINGS}_$roomId")
                    .remove("${KEY_LAST_UPDATE}_$roomId")
            }
            logInfo("Cleared all data for room $roomId")
        } catch (e: Exception) {
            logError("Failed to clear room data", e)
        }
    }

    fun clearAllData() {
        try {
            prefs.edit { clear() }
            logInfo("Cleared all exam data")
        } catch (e: Exception) {
            logError("Failed to clear all data", e)
        }
    }

    fun getStorageStatus(): Map<String, String> {
        val status = mutableMapOf<String, String>()
        try {
            val allKeys = prefs.all.keys
            status["Total Keys"] = allKeys.size.toString()
            
            val roomIds = allKeys.mapNotNull { key ->
                when {
                    key.startsWith(KEY_EXAM_CODES) -> key.substringAfter("${KEY_EXAM_CODES}_")
                    key.startsWith(KEY_NOTIFICATIONS) -> key.substringAfter("${KEY_NOTIFICATIONS}_")
                    key.startsWith(KEY_STUDENT_SEATINGS) -> key.substringAfter("${KEY_STUDENT_SEATINGS}_")
                    else -> null
                }
            }.distinct()
            
            status["Rooms with Data"] = roomIds.size.toString()
            roomIds.forEach { roomId ->
                val examCodes = getExamCodes(roomId).size
                val notification = getNotification(roomId).isNotEmpty()
                val seatings = getStudentSeatings(roomId).size
                val lastUpdate = getLastUpdateTime(roomId)
                
                status["Room $roomId"] = "Exams: $examCodes, Notification: $notification, Seatings: $seatings, Updated: ${if (lastUpdate > 0) "Yes" else "No"}"
            }
        } catch (e: Exception) {
            status["Error"] = e.message ?: "Unknown error"
        }
        return status
    }

}
