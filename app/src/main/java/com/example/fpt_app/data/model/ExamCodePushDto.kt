package com.example.fpt_app.data.model

import com.google.gson.annotations.SerializedName

data class ExamCodePushDto(
    @SerializedName("exams")
    val exams: List<ExamInfo>
) {
    data class ExamInfo(
        @SerializedName("examCode")
        val examCode: String,

        @SerializedName("openCode")
        val openCode: String? = null,

        @SerializedName("roomName")
        val roomName: String,

        @SerializedName("subjectName")
        val subjectName: String,

        @SerializedName("examStart")
        val examStart: String,

        @SerializedName("examEnd")
        val examEnd: String,

        @SerializedName("rules")
        val rules: List<SubjectRuleResponse>? = null
    )

    data class SubjectRuleResponse(
        @SerializedName("subjectRuleId")
        val subjectRuleId: String,

        @SerializedName("examType")
        val examType: String,

        @SerializedName("content")
        val content: String
    )
}
