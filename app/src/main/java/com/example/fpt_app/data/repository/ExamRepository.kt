package com.example.fpt_app.data.repository

import android.content.Context
import android.util.Log
import com.example.fpt_app.data.api.ApiService
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto
import com.example.fpt_app.data.model.Room
import com.example.fpt_app.data.model.Building
import com.example.fpt_app.data.model.CampusData
import com.example.fpt_app.data.network.NetworkConfig
import com.example.fpt_app.data.websocket.WebSocketService
import kotlinx.coroutines.flow.StateFlow
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import okhttp3.OkHttpClient
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.util.concurrent.TimeUnit

class ExamRepository(context: Context) {
    companion object {
        private const val TAG = "ExamRepository"
    }

    private val webSocketService = WebSocketService(context)

    private val apiService: ApiService by lazy {
        try {
            logInfo("Initializing API service with base URL: ${NetworkConfig.BASE_URL}")

            // Create OkHttpClient with SSL bypass for development
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
            })

            val sslContext = SSLContext.getInstance("SSL")
            sslContext.init(null, trustAllCerts, java.security.SecureRandom())

            val okHttpClient = OkHttpClient.Builder()
                .sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
                .hostnameVerifier { _, _ -> true }
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build()

            Retrofit.Builder()
                .baseUrl(NetworkConfig.BASE_URL)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(ApiService::class.java)
        } catch (e: Exception) {
            logError("Failed to initialize API service", e)
            throw e
        }
    }

    val connectionState: StateFlow<WebSocketService.ConnectionState> = webSocketService.connectionState
    val examCodes: StateFlow<List<ExamCodePushDto.ExamInfo>> = webSocketService.examCodes
    val notifications: StateFlow<String> = webSocketService.notifications
    val studentSeatings: StateFlow<List<StudentSeatingPushDto.StudentSeatingInfo>> = webSocketService.studentSeatings

    // Logging utilities
    private fun logInfo(message: String) {
        Log.i(TAG, "INFO: $message")
    }

    private fun logError(message: String, throwable: Throwable? = null) {
        Log.e(TAG, "ERROR: $message", throwable)
    }

    fun connectToRoom(roomId: String) {
        try {
            logInfo("=== WEBSOCKET CONNECTION START ===")
            logInfo("Room ID: $roomId")
            logInfo("WebSocket URL: ${NetworkConfig.WEBSOCKET_URL}")
            logInfo("API Base URL: ${NetworkConfig.BASE_URL}")

            webSocketService.connect(roomId)
            logInfo("WebSocket connection initiated successfully")
        } catch (e: Exception) {
            logError("Failed to initiate WebSocket connection to room: $roomId", e)
        }
    }

    fun disconnect() {
        try {
            logInfo("=== WEBSOCKET DISCONNECT START ===")
            webSocketService.disconnect()
            logInfo("WebSocket disconnected successfully")
        } catch (e: Exception) {
            logError("Error during WebSocket disconnect", e)
        }
    }

    fun testStudentSeating() {
        try {
            logInfo("Testing student seating data")
            webSocketService.testStudentSeating()
            logInfo("Test student seating completed")
        } catch (e: Exception) {
            logError("Failed to test student seating", e)
        }
    }

    fun getStorageStatus(): Map<String, String> {
        return webSocketService.getStorageStatus()
    }

    fun clearNotificationData(roomId: String) {
        try {
            logInfo("Clearing notification data for room: $roomId")
            webSocketService.clearNotificationData(roomId)
            logInfo("Notification data cleared successfully")
        } catch (e: Exception) {
            logError("Failed to clear notification data", e)
        }
    }

    suspend fun getCampusData(): CampusData {
        logInfo("Fetching campus data from API")

        return try {
            val response = apiService.getCampusDetails()

            when {
                response.isSuccessful -> {
                    val body = response.body()
                    if (body?.result != null) {
                        val campusDetail = body.result
                        logInfo("Found ${campusDetail.buildings.size} buildings")

                        val buildings = campusDetail.buildings.map { buildingResponse ->
                            val rooms = buildingResponse.rooms.map { roomDetail ->
                                Room(
                                    id = roomDetail.id,
                                    name = roomDetail.name,
                                    status = roomDetail.status,
                                    createdTime = roomDetail.createdTime,
                                    building = buildingResponse.buildingName,
                                    floor = roomDetail.floor ?: "",
                                )
                            }

                            Building(
                                id = buildingResponse.buildingId,
                                buildingName = buildingResponse.buildingName,
                                rooms = rooms
                            )
                        }

                        val totalRooms = buildings.sumOf { it.rooms.size }
                        logInfo("Loaded ${buildings.size} buildings with $totalRooms rooms")

                        CampusData(
                            campusId = campusDetail.id,
                            campusName = campusDetail.campusName,
                            buildings = buildings
                        )
                    } else {
                        throw Exception("Invalid API response structure")
                    }
                }

                response.code() == 404 -> {
                    throw Exception("API endpoint not found")
                }

                response.code() >= 500 -> {
                    throw Exception("Server error: ${response.code()}")
                }

                else -> {
                    val errorBody = response.errorBody()?.string()
                    logError("API request failed with HTTP ${response.code()}: $errorBody")
                    throw Exception("API request failed: ${response.code()}")
                }
            }

        } catch (e: ConnectException) {
            logError("NETWORK ERROR: Cannot connect to server", e)
            throw Exception("Cannot connect to server. Please check network connection.")

        } catch (e: SocketTimeoutException) {
            logError("TIMEOUT ERROR: Request timed out", e)
            throw Exception("Request timed out. Server may be slow or unavailable.")

        } catch (e: UnknownHostException) {
            logError("DNS ERROR: Cannot resolve host", e)
            throw Exception("Cannot resolve server address. Please check network settings.")

        } catch (e: Exception) {
            logError("UNEXPECTED ERROR during API call", e)
            throw Exception("Failed to fetch campus data: ${e.message}")
        }
    }

}
