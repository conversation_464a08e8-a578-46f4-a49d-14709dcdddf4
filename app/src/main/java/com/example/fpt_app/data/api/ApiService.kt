package com.example.fpt_app.data.api

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

data class ApiResponse<T>(
    val code: Int,
    val message: String? = null,
    val result: T? = null
)

data class RoomDetailResponse(
    val id: String,
    val name: String,
    val status: String,
    val createdTime: String,
    val building: String? = null,
    val floor: String? = null
)

data class BuildingDetailResponse(
    val buildingId: String,
    val locationName: String,
    val buildingName: String
)

data class CampusDetailResponse(
    val id: String,
    val campusName: String,
    val buildings: List<BuildingWithRoomsResponse>
)

data class BuildingWithRoomsResponse(
    val buildingId: String,
    val buildingName: String,
    val rooms: List<RoomDetailResponse>
)

interface ApiService {
    @GET("/api/v1/campus")
    suspend fun getCampusDetails(
        @Query("campusName") campusName: String = "FU-ĐN"
    ): Response<ApiResponse<CampusDetailResponse>>
}
