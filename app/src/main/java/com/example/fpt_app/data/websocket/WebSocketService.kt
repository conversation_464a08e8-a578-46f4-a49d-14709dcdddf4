package com.example.fpt_app.data.websocket

import android.content.Context
import android.util.Log
import com.example.fpt_app.data.model.ExamCodePushDto
import com.example.fpt_app.data.model.StudentSeatingPushDto
import com.example.fpt_app.data.network.NetworkConfig
import com.example.fpt_app.data.network.NetworkDebug
import com.example.fpt_app.data.storage.ExamDataStorage
import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.*
import okio.ByteString
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.util.concurrent.TimeUnit

class WebSocketService(context: Context) {
    companion object {
        private const val TAG = "WebSocketService"
        private const val ROOM_TOPIC_PREFIX = "/topic/room/"
        private const val NOTIFICATION_TOPIC = "/topic/notification"
    }

    private var webSocket: WebSocket? = null
    private val client = createOkHttpClient()
    private val gson = Gson()
    private val examDataStorage = ExamDataStorage(context)

    private fun createOkHttpClient(): OkHttpClient {
        val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
            override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
        })

        val sslContext = SSLContext.getInstance("SSL")
        sslContext.init(null, trustAllCerts, java.security.SecureRandom())

        return OkHttpClient.Builder()
            .sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
            .hostnameVerifier { _, _ -> true }
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    private fun logInfo(message: String) {
        Log.i(TAG, "INFO: $message")
    }

    private fun logError(message: String, throwable: Throwable? = null) {
        Log.e(TAG, "ERROR: $message", throwable)
    }

    private fun logDebug(message: String) {
        Log.d(TAG, "DEBUG: $message")
    }

    private fun logWarning(message: String) {
        Log.w(TAG, "WARNING: $message")
    }

    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()

    private val _examCodes = MutableStateFlow<List<ExamCodePushDto.ExamInfo>>(emptyList())
    val examCodes: StateFlow<List<ExamCodePushDto.ExamInfo>> = _examCodes.asStateFlow()

    private val _notifications = MutableStateFlow<String>("")
    val notifications: StateFlow<String> = _notifications.asStateFlow()

    private val _studentSeatings =
        MutableStateFlow<List<StudentSeatingPushDto.StudentSeatingInfo>>(emptyList())
    val studentSeatings: StateFlow<List<StudentSeatingPushDto.StudentSeatingInfo>> =
        _studentSeatings.asStateFlow()

    private var currentRoomId: String? = null

    fun connect(roomId: String) {
        try {
            logInfo("Connecting to WebSocket for room: $roomId")

            disconnect()
            currentRoomId = roomId

            loadSavedData(roomId)

            if (_examCodes.value.isEmpty()) _examCodes.value = emptyList()
            if (_notifications.value.isEmpty()) _notifications.value = ""
            if (_studentSeatings.value.isEmpty()) _studentSeatings.value = emptyList()

            val url = NetworkConfig.WEBSOCKET_URL
            val request = Request.Builder()
                .url(url)
                .addHeader("roomId", roomId)
                .addHeader("clientType", "room")
                .build()

            val webSocketListener = object : WebSocketListener() {
                override fun onOpen(webSocket: WebSocket, response: Response) {
                    logInfo("WebSocket connected successfully")
                    _connectionState.value = ConnectionState.CONNECTED

                    // Send STOMP CONNECT frame
                    val connectFrame = buildStompFrame(
                        "CONNECT", mapOf(
                            "accept-version" to "1.0,1.1,2.0",
                            "heart-beat" to "10000,10000",
                            "roomId" to roomId,
                            "clientType" to "room"
                        )
                    )

                    webSocket.send(connectFrame)
                }

                override fun onMessage(webSocket: WebSocket, text: String) {
                    handleStompMessage(text, webSocket)
                }

                override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                    logDebug("Received WebSocket bytes: ${bytes.hex()}")
                }

                override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                    logWarning("WebSocket connection closing - Code: $code, Reason: $reason")
                    _connectionState.value = ConnectionState.DISCONNECTED
                }

                override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                    logInfo("WebSocket connection closed - Code: $code, Reason: $reason")
                    _connectionState.value = ConnectionState.DISCONNECTED
                }

                override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                    logError("WebSocket connection failed", t)
                    NetworkDebug.logWebSocketError(t.message ?: "Unknown error")
                    _connectionState.value = ConnectionState.ERROR
                }
            }

            _connectionState.value = ConnectionState.CONNECTING
            webSocket = client.newWebSocket(request, webSocketListener)

        } catch (e: Exception) {
            Log.e("WebSocket", "Failed to connect", e)
            _connectionState.value = ConnectionState.ERROR
        }
    }

    private fun handleStompMessage(message: String, webSocket: WebSocket) {
        try {

            when {
                message.startsWith("CONNECTED") -> {
                    Log.d("WebSocket", "STOMP Connected successfully")
                    subscribeToTopics(webSocket)
                    setDefaultNotification()
                }

                message.startsWith("MESSAGE") -> {
                    Log.d("WebSocket", "📨 Received MESSAGE frame")
                    val body = extractStompBody(message)
                    Log.d("WebSocket", "Extracted body: '$body'")
                    if (body.isNotEmpty()) {
                        handleMessageBody(body)
                    } else {
                        Log.w("WebSocket", "⚠️ MESSAGE frame has empty body")
                    }
                }

                message.startsWith("ERROR") -> {
                    Log.e("WebSocket", "❌ STOMP Error: $message")
                    _connectionState.value = ConnectionState.ERROR
                }

                else -> {
                    Log.d("WebSocket", "❓ Unknown STOMP frame type: ${message.take(50)}...")
                }
            }
        } catch (e: Exception) {
            Log.e("WebSocket", "Exception message: ${e.message}")
        }
    }

    private fun subscribeToTopics(webSocket: WebSocket) {
        currentRoomId?.let { roomId ->
            logInfo("Subscribing to topics for room: $roomId")

            val roomTopic = "$ROOM_TOPIC_PREFIX$roomId"
            val subscribeRoomFrame = buildStompFrame(
                "SUBSCRIBE", mapOf(
                    "id" to "sub-room",
                    "destination" to roomTopic
                )
            )
            webSocket.send(subscribeRoomFrame)

            val subscribeNotificationFrame = buildStompFrame(
                "SUBSCRIBE", mapOf(
                    "id" to "sub-notification",
                    "destination" to NOTIFICATION_TOPIC
                )
            )
            webSocket.send(subscribeNotificationFrame)

            // Subscribe to room-specific notifications
            val roomNotificationTopic = "/topic/notification/$roomId"
            val subscribeRoomNotificationFrame = buildStompFrame(
                "SUBSCRIBE", mapOf(
                    "id" to "sub-room-notification",
                    "destination" to roomNotificationTopic
                )
            )
            webSocket.send(subscribeRoomNotificationFrame)
            logInfo("Subscribed to room-specific notification topic: $roomNotificationTopic")

        } ?: run {
            logError("Cannot subscribe - currentRoomId is null")
        }
    }

    private fun extractStompBody(message: String): String {
        val lines = message.split("\n")
        var body = ""
        var foundEmptyLine = false

        for (line in lines) {
            if (foundEmptyLine) {
                body += line
            } else if (line.isEmpty()) {
                foundEmptyLine = true
            }
        }

        return body.replace("\u0000", "")
    }

    private fun handleMessageBody(body: String) {
        when {
            body.contains("exams") || body.contains("examCode") || body.contains("examCodes") -> {
                logInfo("=== EXAM CODES FROM ADMIN (Room Topic) ===")
                logInfo("Room ID: $currentRoomId")
                try {
                    val examCodePush = gson.fromJson(body, ExamCodePushDto::class.java)
                    val previousCount = _examCodes.value.size
                    _examCodes.value = examCodePush.exams

                    // Save to local storage
                    currentRoomId?.let { roomId ->
                        examDataStorage.saveExamCodes(roomId, examCodePush.exams)
                        logInfo("Exam codes saved to local storage for room: $roomId")
                    }

                    logInfo("EXAM CODES RECEIVED:")
                    logInfo("Previous count: $previousCount")
                    logInfo("New count: ${examCodePush.exams.size}")
                    logInfo("Loading spinner will be replaced with exam codes")

                    examCodePush.exams.forEachIndexed { index, exam ->
                        logInfo("Exam ${index + 1}:")
                        logInfo("  Code: ${exam.examCode}")
                        logInfo("  Subject: ${exam.subjectName}")
                        logInfo("  Time: ${exam.examStart} - ${exam.examEnd}")
                        logInfo("  Open Code: ${exam.openCode ?: "Not set"}")
                    }

                    logInfo("UI automatically updated with exam codes")

                } catch (e: Exception) {
                    logError("Failed to parse exam codes JSON", e)
                    logDebug("Raw exam codes body: $body")
                }
            }

            body.contains("studentSeating's") -> {
                logInfo("Raw JSON body: $body")
                try {
                    logInfo("Attempting to parse StudentSeatingPushDto JSON...")

                    // Safe JSON parsing with null checks
                    val seatingPush = gson.fromJson(body, StudentSeatingPushDto::class.java)

                    if (seatingPush == null) {
                        logError("Parsed seatingPush is null")
                        return
                    }

                    if (false) {
                        logError("studentSeating's array is null")
                        return
                    }

                    val validSeatings = seatingPush.studentSeatings.filter { seating ->
                        seating.mssv.isNotBlank()
                    }

                    _studentSeatings.value = validSeatings

                    // Save to local storage
                    currentRoomId?.let { roomId ->
                        examDataStorage.saveStudentSeatings(roomId, validSeatings)
                        logInfo("Student seating's saved to local storage for room: $roomId")
                    }

                    validSeatings.forEachIndexed { index, seating ->
                        logInfo("Stt ${index + 1}:")
                        logInfo("  MSSV: ${seating.mssv}")
                    }

                } catch (e: Exception) {
                    logError("Exception message: ${e.message}")
                }
            }

            body.isNotEmpty() -> {
                logInfo("=== NOTIFICATION RECEIVED ===")
                logInfo("Raw notification body: '$body'")
                val oldNotification = _notifications.value

                val notificationMessage = try {
                    val jsonObject = gson.fromJson(body, com.google.gson.JsonObject::class.java)
                    if (jsonObject.has("message")) {
                        val message = jsonObject.get("message").asString
                        logInfo("Extracted message from JSON: '$message'")
                        message
                    } else {
                        logWarning("JSON object doesn't have 'message' field, using raw body")
                        body.trim()
                    }
                } catch (e: Exception) {
                    logWarning("Failed to parse notification JSON: ${e.message}")
                    logInfo("Using raw body as fallback: '$body'")
                    body.trim()
                }

                _notifications.value = notificationMessage

                currentRoomId?.let { roomId ->
                    examDataStorage.saveNotification(roomId, notificationMessage)
                    logInfo("Notification saved to local storage for room: $roomId")
                }

                logInfo("Notification updated successfully:")
                logInfo("Previous: '${oldNotification.take(50)}...'")
                logInfo("New: '${notificationMessage.take(50)}...'")
                logInfo("UI will automatically update with new notification")
            }

            else -> {
                logWarning("Received empty message body")
            }
        }
    }

    private fun buildStompFrame(command: String, headers: Map<String, String>, body: String = ""): String {
        val frame = StringBuilder()
        frame.append(command).append("\n")

        headers.forEach { (key, value) ->
            frame.append("$key:$value\n")
        }

        frame.append("\n")
        frame.append(body)
        frame.append("\u0000")

        return frame.toString()
    }

    fun disconnect() {
        try {
            logInfo("=== WEBSOCKET DISCONNECT START ===")

            webSocket?.let { ws ->
                logDebug("Closing WebSocket connection...")
                ws.close(1000, "Client disconnecting")
            } ?: logDebug("No active WebSocket to close")

            webSocket = null
            _connectionState.value = ConnectionState.DISCONNECTED
            currentRoomId = null

            clearData()

            logInfo("WebSocket disconnected and data cleared successfully")

        } catch (e: Exception) {
            logError("Error during WebSocket disconnect", e)

            webSocket = null
            _connectionState.value = ConnectionState.DISCONNECTED
            currentRoomId = null
            clearData()
        }
    }


    private fun setDefaultNotification() {
        if (_notifications.value.isEmpty()) {
            val defaultMessage = "Hệ thống đã kết nối thành công. Đang chờ thông báo từ Admin..."
            _notifications.value = defaultMessage
            logInfo("Set default notification: $defaultMessage")
        }
    }

    private fun clearData() {
        _examCodes.value = emptyList()
        _notifications.value = ""
        _studentSeatings.value = emptyList()
    }

    // Load saved data from storage
    private fun loadSavedData(roomId: String) {
        try {
            logInfo("=== LOADING SAVED DATA FOR ROOM: $roomId ===")

            // Load exam codes
            val savedExamCodes = examDataStorage.getExamCodes(roomId)
            if (savedExamCodes.isNotEmpty()) {
                _examCodes.value = savedExamCodes
                logInfo("Loaded ${savedExamCodes.size} saved exam codes")
            }

            // Load notification
            val savedNotification = examDataStorage.getNotification(roomId)
            if (savedNotification.isNotEmpty()) {
                _notifications.value = savedNotification
                logInfo("Loaded saved notification: ${savedNotification.take(50)}...")
            }

            // Load student seatings
            val savedSeatings = examDataStorage.getStudentSeatings(roomId)
            if (savedSeatings.isNotEmpty()) {
                _studentSeatings.value = savedSeatings
                logInfo("Loaded ${savedSeatings.size} saved student seatings")
            }

            val lastUpdate = examDataStorage.getLastUpdateTime(roomId)
            if (lastUpdate > 0) {
                val timeAgo = (System.currentTimeMillis() - lastUpdate) / 1000 / 60
                logInfo("Data was last updated $timeAgo minutes ago")
            }

        } catch (e: Exception) {
            logError("Failed to load saved data", e)
        }
    }

    fun testStudentSeating() {
        val testSeatings = listOf(
            StudentSeatingPushDto.StudentSeatingInfo(mssv = "SE161001"),
            StudentSeatingPushDto.StudentSeatingInfo(mssv = "SE161002"),
            StudentSeatingPushDto.StudentSeatingInfo(mssv = "SE161003"),
            StudentSeatingPushDto.StudentSeatingInfo(mssv = "SE161004"),
            StudentSeatingPushDto.StudentSeatingInfo(mssv = "SE161005")
        )

        _studentSeatings.value = testSeatings
        logInfo("Test student seating data set: ${testSeatings.size} students")
        testSeatings.forEach { seating ->
            logInfo("  MSSV: ${seating.mssv}")
        }
    }

    fun getStorageStatus(): Map<String, String> {
        return examDataStorage.getStorageStatus()
    }

    fun clearNotificationData(roomId: String) {
        try {
            logInfo("Clearing notification data for room: $roomId")
            // Clear from storage
            examDataStorage.saveNotification(roomId, "")
            // Clear from current state
            _notifications.value = ""
            logInfo("Notification data cleared for room: $roomId")
        } catch (e: Exception) {
            logError("Failed to clear notification data", e)
        }
    }

    enum class ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        ERROR
    }
}
