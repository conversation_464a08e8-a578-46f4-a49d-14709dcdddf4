package com.example.fpt_app.data.storage

import android.content.Context
import android.content.SharedPreferences
import com.example.fpt_app.data.model.Room
import com.google.gson.Gson
import androidx.core.content.edit

class RoomStorage(context: Context) {
    companion object {
        private const val PREF_NAME = "room_storage"
        private const val KEY_SELECTED_ROOM_ID = "selected_room_id"
        private const val KEY_SELECTED_ROOM_DATA = "selected_room_data"
        private const val KEY_SELECTED_BUILDING = "selected_building"
        private const val TAG = "RoomStorage"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    // Logging utilities
    private fun logInfo(message: String) {
        android.util.Log.i(TAG, "INFO: $message")
    }
    
    private fun logDebug(message: String) {
        android.util.Log.d(TAG, "DEBUG: $message")
    }
    
    private fun logError(message: String, throwable: Throwable? = null) {
        android.util.Log.e(TAG, "ERROR: $message", throwable)
    }
    
    // Save selected room
    fun saveSelectedRoom(room: Room) {
        try {
            logInfo("=== SAVING SELECTED ROOM ===")
            logInfo("Room ID: ${room.id}")
            logInfo("Room Name: ${room.name}")
            logInfo("Building: ${room.building}")

            sharedPreferences.edit {
                putString(KEY_SELECTED_ROOM_ID, room.id)
                putString(KEY_SELECTED_ROOM_DATA, gson.toJson(room))
                putString(KEY_SELECTED_BUILDING, room.building)
            }
            
            logInfo("Room saved successfully to SharedPreferences")
            
        } catch (e: Exception) {
            logError("Failed to save selected room", e)
        }
    }
    
    // Get saved room ID
    fun getSavedRoomId(): String? {
        return try {
            val roomId = sharedPreferences.getString(KEY_SELECTED_ROOM_ID, null)
            logDebug("Retrieved saved room ID: $roomId")
            roomId
        } catch (e: Exception) {
            logError("Failed to get saved room ID", e)
            null
        }
    }
    
    // Get saved room data
    fun getSavedRoom(): Room? {
        return try {
            val roomJson = sharedPreferences.getString(KEY_SELECTED_ROOM_DATA, null)
            if (roomJson != null) {
                val room = gson.fromJson(roomJson, Room::class.java)
                logInfo("Retrieved saved room: ${room.name} (${room.id})")
                room
            } else {
                logDebug("No saved room found")
                null
            }
        } catch (e: Exception) {
            logError("Failed to get saved room data", e)
            null
        }
    }
    
    // Get saved building
    fun getSavedBuilding(): String? {
        return try {
            val building = sharedPreferences.getString(KEY_SELECTED_BUILDING, null)
            logDebug("Retrieved saved building: $building")
            building
        } catch (e: Exception) {
            logError("Failed to get saved building", e)
            null
        }
    }
    
    // Check if room is already saved
    fun hasSelectedRoom(): Boolean {
        return try {
            val hasRoom = sharedPreferences.contains(KEY_SELECTED_ROOM_ID) && 
                         !sharedPreferences.getString(KEY_SELECTED_ROOM_ID, "").isNullOrEmpty()
            logDebug("Has selected room: $hasRoom")
            hasRoom
        } catch (e: Exception) {
            logError("Failed to check if room is saved", e)
            false
        }
    }
    
    // Clear saved room (for reset/logout)
    fun clearSelectedRoom() {
        try {
            logInfo("=== CLEARING SAVED ROOM ===")
            sharedPreferences.edit {
                remove(KEY_SELECTED_ROOM_ID)
                remove(KEY_SELECTED_ROOM_DATA)
                remove(KEY_SELECTED_BUILDING)
            }
            logInfo("Saved room cleared successfully")
        } catch (e: Exception) {
            logError("Failed to clear saved room", e)
        }
    }
    
    // Get storage status for debugging
    fun getStorageStatus(): Map<String, String> {
        return try {
            mapOf(
                "hasSelectedRoom" to hasSelectedRoom().toString(),
                "savedRoomId" to (getSavedRoomId() ?: "null"),
                "savedBuilding" to (getSavedBuilding() ?: "null"),
                "savedRoomName" to (getSavedRoom()?.name ?: "null")
            )
        } catch (e: Exception) {
            logError("Failed to get storage status", e)
            mapOf("error" to e.message.toString())
        }
    }

}
