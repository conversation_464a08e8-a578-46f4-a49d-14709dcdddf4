package com.example.fpt_app.data.network

import android.util.Log

object NetworkDebug {
    private const val TAG = "NetworkDebug"
    
    fun logNetworkConfig() {
        Log.d(TAG, "=== NETWORK CONFIGURATION ===")
        Log.d(TAG, "API Base URL: ${NetworkConfig.BASE_URL}")
        Log.d(TAG, "WebSocket URL: ${NetworkConfig.WEBSOCKET_URL}")
        Log.d(TAG, "=============================")
    }
    
    fun logConnectionAttempt(roomId: String) {
        Log.d(TAG, "=== CONNECTION ATTEMPT ===")
        Log.d(TAG, "Room ID: $roomId")
        Log.d(TAG, "API URL: ${NetworkConfig.BASE_URL}/api/v1/rooms")
        Log.d(TAG, "WebSocket URL: ${NetworkConfig.WEBSOCKET_URL}")
        Log.d(TAG, "==========================")
    }
    
    fun logWebSocketError(error: String) {
        Log.e(TAG, "=== WEBSOCKET ERROR ===")
        Log.e(TAG, "Error: $error")
        Log.e(TAG, "Current URL: ${NetworkConfig.WEBSOCKET_URL}")
        Log.e(TAG, "Suggestions:")
        Log.e(TAG, "1. Check if backend is running on port 8080")
        Log.e(TAG, "2. Verify IP address in NetworkConfig.kt")
        Log.e(TAG, "3. For emulator: use ********:8080")
        Log.e(TAG, "4. For real device: use computer's IP address")
        Log.e(TAG, "5. Ensure same WiFi network for real device")
        Log.e(TAG, "======================")
    }
}
