# 📺 Hướng dẫn Setup TV Display

## 🔌 Cách kết nối Android với TV

### **Phương pháp 1: Cáp HDMI (Khuyến nghị)**
1. **Chuẩn bị**: Cáp USB-C to HDMI hoặc Micro USB to HDMI
2. **Kết nối**: Android device → Cáp HDMI → TV
3. **Cài đặt TV**: Chọn input HDMI tương ứng
4. **Cài đặt Android**: Settings → Display → Cast/Screen Mirroring

### **Phương pháp 2: Wireless Display (Miracast)**
1. **TV hỗ trợ Miracast**: Bật tính năng Screen Mirroring
2. **Android**: Settings → Connected devices → Cast
3. **Chọn TV** từ danh sách thiết bị
4. **Kết nối** và chờ mirror

### **Phương pháp 3: Chromecast**
1. **Chuẩn bị**: Chromecast device kết nối TV
2. **Cài đặt**: Google Home app trên Android
3. **Cast**: Mở app → Cast icon → Chọn Chromecast
4. **Mirror**: Cast entire screen

### **Phương pháp 4: Android TV Box**
1. **Cài đặt**: APK file trực tiếp lên Android TV Box
2. **Chạy app**: Từ launcher của TV Box
3. **Điều khiển**: Remote TV hoặc wireless mouse/keyboard

## 🎯 Tối ưu cho TV Display

### **Cài đặt Android:**
- **Screen timeout**: Never (Không bao giờ tắt màn hình)
- **Brightness**: 100% (Độ sáng tối đa)
- **Orientation**: Landscape (Ngang)
- **Navigation**: Hide navigation bar (Ẩn thanh điều hướng)

### **Cài đặt TV:**
- **Picture mode**: Vivid hoặc Dynamic
- **Aspect ratio**: 16:9 hoặc Just Scan
- **Overscan**: Off (Tắt)

## 📱 App Settings cho TV

### **Trong app, thêm các tính năng:**

```kotlin
// Giữ màn hình sáng
window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

// Ẩn system UI
window.decorView.systemUiVisibility = (
    View.SYSTEM_UI_FLAG_FULLSCREEN
    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
)

// Landscape orientation
requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
```

## 🖥️ TV Layout Specifications

### **Kích thước hiện tại:**
- **Thông báo**: 25% màn hình (khi có)
- **Mã thi**: 45-65% màn hình (responsive)
- **Nội quy**: 30% màn hình (cố định)

### **Typography cho TV:**
- **Tiêu đề**: `headlineLarge` (32sp)
- **Mã thi**: `headlineMedium` (28sp)
- **Nội dung**: `titleLarge` (22sp)
- **Chi tiết**: `bodyLarge` (16sp)

### **Spacing cho TV:**
- **Padding**: 24-32dp
- **Margin**: 16-20dp
- **Icon size**: 40-48dp

## 🧪 Test Scenarios

### **Test 1: Layout Responsive**
1. Mở app → TV Test Screen
2. Nhấn "Ẩn TB" → Kiểm tra mã thi mở rộng
3. Nhấn "Hiện TB" → Kiểm tra layout 3 phần

### **Test 2: Readability từ xa**
1. Đứng cách TV 3-5 mét
2. Kiểm tra đọc được mã thi
3. Kiểm tra đọc được thông báo
4. Kiểm tra đọc được nội quy

### **Test 3: Real-time Updates**
1. Vào Room Selection → Chọn phòng
2. Kiểm tra WebSocket connection
3. Test nhận data từ backend

## 🔧 Troubleshooting

### **Màn hình bị cắt:**
- Tắt Overscan trên TV
- Chọn "Just Scan" hoặc "Screen Fit"

### **Text quá nhỏ:**
- Tăng font size trong app
- Sử dụng `headlineLarge` thay vì `titleMedium`

### **App không fullscreen:**
- Thêm immersive mode
- Ẩn status bar và navigation bar

### **Kết nối không ổn định:**
- Sử dụng cáp HDMI thay vì wireless
- Kiểm tra WiFi signal strength

## 📏 Recommended TV Sizes

- **Minimum**: 32 inch (cho phòng nhỏ)
- **Optimal**: 43-55 inch (cho phòng thi tiêu chuẩn)
- **Maximum**: 65+ inch (cho phòng thi lớn)

## 🎨 Color Scheme cho TV

- **Background**: Blue gradient (#1976D2 → #42A5F5)
- **Cards**: White (#FFFFFF)
- **Notification**: Light red (#FFEBEE)
- **Exam codes**: Light green (#E8F5E8)
- **Text**: High contrast (#333333, #FFFFFF)

Perfect cho phòng thi FPT! 🎓📺
