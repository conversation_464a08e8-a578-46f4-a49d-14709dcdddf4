# 📺 Ứng dụng TV Display Phòng Thi - FPT University

Ứng dụng Android được thiết kế đặc biệt cho **TV Display** với layout cố định 3 phần, hiển thị thông báo, mã thi và nội quy phòng thi với tích hợp WebSocket real-time.

## 🖥️ Thiết kế TV Display

### Layout 3 phần cố định (không scroll):
1. **🔔 THÔNG BÁO** (25% màn hình) - Chỉ hiện khi có thông báo
2. **📝 MÃ THI** (45-65% màn hình) - Tự động mở rộng khi không có thông báo  
3. **⚖️ NỘI QUY** (30% màn hình) - Cố định ở dưới

### Responsive Design:
- **C<PERSON> thông báo**: 25% - 45% - 30%
- **Không thông báo**: 0% - 65% - 30%
- **1 mã thi**: Hiển thị lớn ở giữa
- **Nhi<PERSON><PERSON> mã thi**: Grid 2 cột

## 🚀 Tính năng

- ✅ **TV-optimized UI**: Typography lớn, padding rộng
- ✅ **Real-time WebSocket**: Nhận data tức thì từ Spring Boot
- ✅ **Room Selection**: Chọn phòng để subscribe topic riêng
- ✅ **Professional Design**: Material Design 3, gradient background
- ✅ **No Scroll**: Tất cả hiển thị trên 1 màn hình

## 🔧 Cấu hình Backend

### Spring Boot WebSocket Configuration:
```java
@Override
public void registerStompEndpoints(StompEndpointRegistry registry) {
    registry.addEndpoint("/ws")
            .setAllowedOrigins("*") // Cho phép Android kết nối
            .setAllowedOriginPatterns("*")
            .addInterceptors(new WebSocketHandshakeInterceptor())
            .withSockJS();
}
```

### Topics:
- `/topic/room/{roomId}` - Mã thi cho phòng cụ thể
- `/topic/notification` - Thông báo chung

## 🌐 Cấu hình Network

### Cấu hình IP cho Android:
Mở file `NetworkConfig.kt` và cấu hình:

```kotlin
// Option 1: Android Emulator
private const val USE_EMULATOR = true  // Sử dụng ********:8080

// Option 2: Real Device
private const val USE_EMULATOR = false // Sử dụng IP thực của máy tính
private const val REAL_DEVICE_IP = "************:8080" // Thay đổi IP này
```

### Hướng dẫn tìm IP máy tính:
- **Windows**: Mở CMD → gõ `ipconfig` → tìm IPv4 Address
- **Mac/Linux**: Mở Terminal → gõ `ifconfig` → tìm inet address
- **Ví dụ**: ************, *************, *********

### Lưu ý quan trọng:
- ❌ **Không dùng localhost** trên mobile
- ✅ **Android Emulator**: Dùng `********:8080`
- ✅ **Real Device**: Dùng IP thực của máy tính + cùng mạng WiFi

## 🔧 Troubleshooting

### Lỗi WebSocket HTTP 400:
```
Expected HTTP 101 response but was '400'
```

**Nguyên nhân & Giải pháp:**

1. **Backend chưa chạy**
   ```bash
   # Kiểm tra backend có chạy không
   curl http://********:8080/api/v1/rooms
   # hoặc
   curl http://192.168.1.X:8080/api/v1/rooms
   ```

2. **IP address sai**
   - Mở `NetworkConfig.kt`
   - Thay đổi `USE_EMULATOR` và `REAL_DEVICE_IP`
   - Restart app

3. **WebSocket endpoint sai**
   - Kiểm tra Spring Boot có endpoint `/ws` không
   - Đảm bảo SockJS được enable

4. **Firewall/Network**
   - Tắt firewall tạm thời để test
   - Đảm bảo cùng mạng WiFi (real device)

### Debug Logs:
App sẽ tự động log network configuration. Xem trong Logcat:
```
NetworkDebug: === NETWORK CONFIGURATION ===
NetworkDebug: API Base URL: http://********:8080
NetworkDebug: WebSocket URL: ws://********:8080/ws/websocket
```

## 📱 Cách sử dụng

### 🧪 **Test Mode (Khuyến nghị)**:
1. **Mở app** → Tự động vào TV Test Screen
2. **Nhấn "Ẩn TB/Hiện TB"** để test responsive layout
3. **Xem layout 3 phần** với dữ liệu mẫu

### 🚀 **Production Mode**:
1. **Kết nối Android với TV** (HDMI/Chromecast)
2. **Từ Test Screen** → Nhấn Back → Chọn phòng thi
3. **App tự động kết nối WebSocket** và hiển thị real-time
4. **Sinh viên xem từ xa** - không cần tương tác

## 🌐 Network Configuration

**IP hiện tại**: `************:8080`

Để thay đổi IP, cập nhật trong:
- `WebSocketService.kt` (line 33)
- `ExamRepository.kt` (line 16)

## 📊 Data Format & Test Data

### 🧪 **Hard-coded Test Data**:
- **Thông báo**: "🚨 THÔNG BÁO QUAN TRỌNG: Sinh viên vui lòng chuẩn bị đầy đủ giấy tờ tùy thân..."
- **Mã thi**: 3 exam codes (EX2024001, EX2024002, EX2024003)
- **Môn học**: Java Spring Boot, MySQL, Android Development

### 📡 **WebSocket Data Format**:

**Exam Code Push:**
```json
{
  "exams": [
    {
      "examCode": "EX2024001",
      "roomName": "Phòng A101",
      "subjectName": "Lập trình Java Spring Boot",
      "examStart": "08:00",
      "examEnd": "10:00"
    }
  ]
}
```

**Notification:**
```json
{
  "message": "🚨 THÔNG BÁO QUAN TRỌNG: Sinh viên vui lòng chuẩn bị..."
}
```

## 🎨 UI Components

- **TVDisplayLayout**: Main layout với 3 phần responsive
- **TVNotificationSection**: Thông báo với background đỏ nhạt
- **TVExamCodesSection**: Mã thi với background trắng
- **TVExamRulesSection**: Nội quy compact với badges
- **TVSingleExamCode**: Display lớn cho 1 mã thi
- **TVMultipleExamCodes**: Grid layout cho nhiều mã thi

## 🔥 Highlights

- 🎯 **Zero Scroll**: Tất cả content fit trong 1 màn hình
- 📏 **Smart Sizing**: Auto-resize dựa trên content
- 🎨 **Professional**: Gradient, shadows, proper spacing
- ⚡ **Real-time**: WebSocket với Spring Boot STOMP
- 📱 **TV Ready**: Large fonts, high contrast colors

Perfect cho phòng thi FPT University! 🎓
