# Backend Cronjob Changes for Real-time Student Seating

## Required Changes

### 1. Update StudentSeatingPushDto.java

The DTO field name should be `studentCode` instead of `mssv` to match Android expectations:

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public static class StudentSeatingInfo {
    private String studentId;
    private String studentName;
    private String studentCode; // Changed from 'mssv' to 'studentCode'
    private String className;
    private String subjectName;
    private String roomName;
    private String examStart;
    private String examEnd;
    private String note;
}
```

### 2. Update PushStudentSeatingCronjob.java

Update the cronjob to use the correct field name:

```java
private void processBatch(List<StudentExamAssignment> assignmentBatch) {
    Map<String, List<StudentSeatingPushDto.StudentSeatingInfo>> seatingsByTopic = new HashMap<>();

    assignmentBatch.forEach(assignment -> {
        StudentSeatingPushDto.StudentSeatingInfo seatingInfo = StudentSeatingPushDto.StudentSeatingInfo.builder()
                .studentId(assignment.getStudent().getId())
                .studentName(assignment.getStudent().getFullName())
                .studentCode(assignment.getStudent().getMssv()) // Use studentCode field
                .className(assignment.getStudent().getClassName())
                .subjectName(assignment.getSubject().getName())
                .roomName(assignment.getRoom().getName())
                .examStart(assignment.getStartTime().format(FORMATTER))
                .examEnd(assignment.getEndTime().format(FORMATTER))
                .note(assignment.getNote())
                .build();

        String topic = "/topic/room/" + assignment.getRoom().getId();
        seatingsByTopic.computeIfAbsent(topic, k -> new ArrayList<>()).add(seatingInfo);

        assignment.setStatus(AssignmentStatus.SENT);
    });

    seatingsByTopic.forEach((topic, seatingInfos) -> {
        try {
            StudentSeatingPushDto pushDto = StudentSeatingPushDto.builder()
                    .studentSeatings(seatingInfos)
                    .build();
            simpMessagingTemplate.convertAndSend(topic, pushDto);
            log.info("Successfully sent {} student seating records to topic {}", seatingInfos.size(), topic);
        } catch (Exception e) {
            log.error("Failed to send student seating to topic {}: {}", topic, e.getMessage());
        }
    });
}
```

## How It Works

### 1. Data Flow
1. **Cronjob runs** → Finds pending student assignments
2. **Process assignments** → Creates seating info with real student data
3. **Send to WebSocket** → Pushes to `/topic/room/{roomId}`
4. **Android receives** → Updates seating chart in real-time

### 2. Key Features
- **Real-time updates**: No hardcoded data, uses actual student assignments
- **Last 3 digits display**: Android app automatically shows last 3 digits of MSSV
- **Loading states**: Android shows spinner until data arrives
- **Batch processing**: Efficient handling of multiple students
- **Error handling**: Graceful failure handling per batch

### 3. Message Format
The WebSocket message sent to Android will be:
```json
{
  "studentSeatings": [
    {
      "studentId": "123",
      "studentName": "Nguyễn Văn A",
      "studentCode": "SE161001",
      "className": "SE1601",
      "subjectName": "Lập trình Mobile Android",
      "roomName": "Phòng A101",
      "examStart": "08:00",
      "examEnd": "10:00",
      "note": null
    }
  ]
}
```

### 4. Android Processing
- Receives message via WebSocket
- Extracts last 3 digits of `studentCode` (e.g., "001" from "SE161001")
- Maps to seat positions in 6×4 grid
- Updates UI with occupied seats showing student codes
- Empty seats show seat numbers

## Testing

### Backend Testing
1. Ensure student assignments exist in database
2. Run cronjob manually or wait for scheduled execution
3. Check logs for successful WebSocket sends
4. Verify message format matches expected JSON structure

### Android Testing
1. Connect to room via Android app
2. Wait for seating data or use test function: `repository.testStudentSeating()`
3. Verify seating chart shows:
   - Loading spinner initially
   - Student codes (last 3 digits) in occupied seats
   - Seat numbers in empty seats
   - Different colors for occupied vs empty seats

## Notes

- The Android app expects `studentCode` field name, not `mssv`
- Only the last 3 digits of MSSV are displayed for clarity
- Seating grid is fixed at 6 rows × 4 columns (24 seats)
- Real-time updates happen automatically when backend sends new data
- Error handling ensures individual batch failures don't break the entire process
