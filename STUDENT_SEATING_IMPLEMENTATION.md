# Student Seating Real-time Implementation

## Overview
This implementation adds real-time student seating display to the exam room display system. The seating chart now shows the last 3 digits of student MSSV (student code) instead of hardcoded seat numbers, and includes loading states similar to exam codes.

## Changes Made

### Android App Changes

#### 1. New Data Model
- **File**: `app/src/main/java/com/example/fpt_app/data/model/StudentSeatingPushDto.kt`
- **Purpose**: Defines the data structure for student seating information received from backend
- **Key Fields**:
  - `studentId`: Student identifier
  - `studentName`: Full student name
  - `studentCode`: MSSV (student code)
  - `className`: Student's class
  - `subjectName`: Subject name
  - `roomName`: Room name
  - `examStart`/`examEnd`: Exam time
  - `note`: Optional notes

#### 2. WebSocket Service Updates
- **File**: `app/src/main/java/com/example/fpt_app/data/websocket/WebSocketService.kt`
- **Changes**:
  - Added `_studentSeatings` StateFlow to manage seating data
  - Added message handling for `studentSeatings` JSON messages
  - Added test function `testStudentSeating()` for development
  - Clear seating data on disconnect

#### 3. Repository Updates
- **File**: `app/src/main/java/com/example/fpt_app/data/repository/ExamRepository.kt`
- **Changes**:
  - Exposed `studentSeatings` StateFlow
  - Added `testStudentSeating()` method

#### 4. ViewModel Updates
- **File**: `app/src/main/java/com/example/fpt_app/ui/viewmodel/ExamDisplayViewModel.kt`
- **Changes**:
  - Added `studentSeatings` StateFlow exposure

#### 5. UI Component Updates

##### SeatingChartSection
- **File**: `app/src/main/java/com/example/fpt_app/ui/components/SeatingChartSection.kt`
- **Major Changes**:
  - Updated `SeatInfo` to include student information
  - Added loading spinner when no seating data available
  - Display last 3 digits of MSSV instead of seat numbers
  - Smaller seat size (45dp instead of 55dp) to make room for larger exam codes
  - Different colors for occupied vs empty seats
  - Real-time data processing from `StudentSeatingPushDto`

##### Layout Updates
- **File**: `app/src/main/java/com/example/fpt_app/ui/components/ExamDisplayLayout.kt`
- **Changes**:
  - Added `studentSeatings` parameter to layout functions
  - Adjusted column weights: Left column 50% (increased), Seating 25% (decreased), Right column 25%
  - Pass seating data to `SeatingChartSection`

##### Screen Updates
- **File**: `app/src/main/java/com/example/fpt_app/ui/screen/ExamDisplayScreen.kt`
- **Changes**:
  - Collect `studentSeatings` from ViewModel
  - Pass seating data to layout components

### Backend Changes Required

#### 1. Update StudentSeatingPushDto (Already provided)
The backend DTO should match the Android model:
```java
public static class StudentSeatingInfo {
    private String studentId;
    private String studentName;
    private String studentCode; // MSSV
    private String className;
    private String subjectName;
    private String roomName;
    private String examStart;
    private String examEnd;
    private String note;
}
```

#### 2. Update Cronjob Logic
The cronjob should:
- Send real-time student assignment data instead of hardcoded data
- Use actual student MSSV from database
- Send data to WebSocket topic `/topic/room/{roomId}`
- Message format should contain `studentSeatings` field

#### 3. WebSocket Topic
- **Topic**: `/topic/room/{roomId}`
- **Message Type**: JSON with `studentSeatings` array
- **Trigger**: When student assignments are ready to be displayed

## Features

### 1. Real-time Updates
- Seating chart updates automatically when backend sends new data
- No manual refresh required

### 2. Loading States
- Shows spinner and "Đang tải sơ đồ chỗ ngồi" when no data
- Similar to exam codes loading behavior

### 3. Visual Indicators
- **Occupied seats**: Light blue background with blue border
- **Empty seats**: White background with gray border
- **Display**: Last 3 digits of MSSV for occupied seats, seat number for empty

### 4. Responsive Layout
- Seating area reduced to 25% width (from 30%)
- Exam codes area increased to 50% width (from 45%)
- Maintains professional appearance

### 5. Test Functions
- `testStudentSeating()` creates sample data for development
- Shows students in seats 001, 002, 005 with realistic Vietnamese names

## Usage

### For Development/Testing
```kotlin
// In your test code or debug menu
repository.testStudentSeating()
```

### For Production
The system automatically receives real-time data from the backend cronjob when:
1. Student assignments are processed
2. Backend sends data to `/topic/room/{roomId}`
3. Message contains `studentSeatings` field

## Data Flow

1. **Backend Cronjob** → Processes student assignments
2. **WebSocket Message** → Sends to `/topic/room/{roomId}`
3. **Android WebSocketService** → Receives and parses JSON
4. **StateFlow Update** → Triggers UI recomposition
5. **SeatingChartSection** → Displays updated seating arrangement

## Notes

- Seating grid is 6 rows × 4 columns (24 seats total)
- Only last 3 digits of MSSV are displayed for privacy and clarity
- Loading state prevents empty/broken UI during data fetch
- Maintains consistency with existing exam codes loading pattern
